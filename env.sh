#!/bin/bash

# --- Configuration ---
# Your Cloud Run service name
SERVICE_NAME="hello-world"
# Your Cloud Run region (e.g., asia-south1)
REGION="asia-south1"
# Your Google Cloud Project ID
PROJECT_ID=$(gcloud config get-value project)
# Path to your .env file
ENV_FILE="./.env"
# Your Docker image path
# Make sure you've already built and pushed this image
IMAGE_PATH="$REGION-docker.pkg.dev/$PROJECT_ID/cloud-run-repo/hello-world:latest"

# --- Script Logic ---

# Check if the .env file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Error: .env file not found at $ENV_FILE"
    exit 1
fi

# Initialize an empty string for environment variables
ENV_VARS=""

# Read the .env file line by line
while IFS='=' read -r key value || [ -n "$key" ]; do
    # Skip empty lines and comments
    if [[ -z "$key" || "$key" =~ ^# ]]; then
        continue
    fi

    # Trim whitespace from key and value
    key=$(echo "$key" | xargs)
    value=$(echo "$value" | xargs)

    # Append to the ENV_VARS string in KEY=VALUE format, comma-separated
    if [ -z "$ENV_VARS" ]; then
        ENV_VARS="${key}=${value}"
    else
        ENV_VARS="${ENV_VARS},${key}=${value}"
    fi
done < "$ENV_FILE"

# Check if any variables were parsed
if [ -z "$ENV_VARS" ]; then
    echo "No environment variables found in $ENV_FILE to set."
    exit 0
fi

echo "Parsed environment variables:"
echo "$ENV_VARS"
echo ""

echo "Deploying Cloud Run service '$SERVICE_NAME' with parsed environment variables..."

# Deploy the Cloud Run service with all parsed environment variables
# Added timeout and resource settings for background tasks and Playwright browsers
gcloud run deploy "$SERVICE_NAME" \
    --image "$IMAGE_PATH" \
    --platform managed \
    --region "$REGION" \
    --set-env-vars "$ENV_VARS" \
    --allow-unauthenticated \
    --port 8080 \
    --timeout=3600 \
    --cpu=4 \
    --memory=8Gi \
    --concurrency=5 \
    --max-instances=10

echo "Deployment command executed. Check your Cloud Run service for status."