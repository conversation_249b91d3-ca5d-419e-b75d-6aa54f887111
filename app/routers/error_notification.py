"""
Error notification router for sending email alerts about system errors
"""
from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
import logging
from datetime import datetime

from app.utils.email_notification_service import send_error_notification
from app.utils.logger import ConsoleLogger


router = APIRouter()
logger = logging.getLogger(__name__)


class ErrorNotificationRequest(BaseModel):
    """
    Request model for error notification
    """
    org_id: str = Field(..., description="Organization ID", min_length=1)
    tool_name: str = Field(..., description="Name of the tool/analysis that failed", min_length=1)
    error_message: str = Field(..., description="Error message", min_length=1)
    additional_context: Optional[Dict[str, Any]] = Field(
        None, 
        description="Additional context information (optional)"
    )
    log_to_db: bool = Field(
        True, 
        description="Whether to log the error to database (default: True)"
    )


class ErrorNotificationResponse(BaseModel):
    """
    Response model for error notification
    """
    success: bool
    message: str
    org_id: str
    tool_name: str
    timestamp: str


@router.post("/send", response_model=ErrorNotificationResponse)
async def send_error_notification_endpoint(request: ErrorNotificationRequest):
    """
    Send error notification email for a specific organization and tool
    
    This endpoint allows sending error notifications via email when analysis tools fail.
    It's designed to be used by specific clients/organizations that want error alerting.
    
    Args:
        request (ErrorNotificationRequest): Error notification details
        
    Returns:
        ErrorNotificationResponse: Result of the notification attempt
        
    Raises:
        HTTPException: If the request is invalid or notification fails
    """
    console_logger = ConsoleLogger("ErrorNotificationRouter")
    
    try:
        # Validate request
        if not request.org_id or not request.tool_name or not request.error_message:
            console_logger.error("Invalid request: missing required fields")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid request: org_id, tool_name, and error_message are required"
            )
        
        console_logger.info(
            "Processing error notification request",
            {
                "org_id": request.org_id,
                "tool_name": request.tool_name,
                "has_additional_context": bool(request.additional_context),
                "log_to_db": request.log_to_db
            }
        )
        
        # Send error notification
        success = send_error_notification(
            org_id=request.org_id,
            tool_name=request.tool_name,
            error_message=request.error_message,
            additional_context=request.additional_context,
            log_to_db=request.log_to_db
        )
        
        if success:
            console_logger.info(
                "Error notification sent successfully",
                {
                    "org_id": request.org_id,
                    "tool_name": request.tool_name
                }
            )
            
            return ErrorNotificationResponse(
                success=True,
                message="Error notification sent successfully",
                org_id=request.org_id,
                tool_name=request.tool_name,
                timestamp=str(datetime.now().isoformat())
            )
        else:
            console_logger.error(
                "Failed to send error notification",
                {
                    "org_id": request.org_id,
                    "tool_name": request.tool_name
                }
            )
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send error notification. Please check email configuration."
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        console_logger.error(
            "Unexpected error in error notification endpoint",
            {
                "org_id": request.org_id if hasattr(request, 'org_id') else "unknown",
                "tool_name": request.tool_name if hasattr(request, 'tool_name') else "unknown",
                "error": str(e)
            }
        )
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/test")
async def test_error_notification():
    """
    Test endpoint to verify error notification functionality
    
    This endpoint sends a test error notification to verify the email configuration
    and functionality. Useful for testing during setup.
    
    Returns:
        dict: Test result
    """
    console_logger = ConsoleLogger("ErrorNotificationTest")
    
    try:
        console_logger.info("Testing error notification functionality")
        
        # Send test notification
        success = send_error_notification(
            org_id="test_org",
            tool_name="test_tool",
            error_message="This is a test error notification to verify email functionality",
            additional_context={
                "test_type": "functionality_test",
                "timestamp": str(datetime.now().isoformat()),
                "endpoint": "/error-notification/test"
            },
            log_to_db=True
        )
        
        if success:
            console_logger.info("Test error notification sent successfully")
            return {
                "success": True,
                "message": "Test error notification sent successfully",
                "timestamp": str(datetime.now().isoformat())
            }
        else:
            console_logger.error("Test error notification failed")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Test error notification failed. Please check email configuration."
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        console_logger.error(f"Unexpected error in test endpoint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Test failed with error: {str(e)}"
        )


@router.get("/config")
async def get_notification_config():
    """
    Get current error notification configuration (without sensitive data)

    Returns:
        dict: Configuration status
    """
    from app.config import settings

    return {
        "smtp_server": settings.SMTP_SERVER,
        "smtp_port": settings.SMTP_PORT,
        "smtp_username_configured": bool(settings.SMTP_USERNAME),
        "smtp_password_configured": bool(settings.SMTP_PASSWORD),
        "notification_email": settings.ERROR_NOTIFICATION_EMAIL,
        "from_name": settings.EMAIL_FROM_NAME,
        "configuration_complete": bool(
            settings.SMTP_USERNAME and
            settings.SMTP_PASSWORD and
            settings.ERROR_NOTIFICATION_EMAIL
        )
    }


@router.post("/system-issue")
async def send_system_issue_notification(
    issue_type: str,
    issue_message: str,
    component: str,
    severity: str = "HIGH",
    additional_context: Optional[Dict[str, Any]] = None
):
    """
    Send system-wide issue notification for monitoring system health

    This endpoint allows manual triggering of system monitoring notifications
    for various system issues like database problems, API failures, etc.

    Args:
        issue_type (str): Type of system issue (database, api, network, celery, etc.)
        issue_message (str): Detailed error message
        component (str): System component affected
        severity (str): Issue severity (LOW, MEDIUM, HIGH, CRITICAL)
        additional_context (Dict, optional): Additional context information

    Returns:
        dict: Result of the notification attempt
    """
    console_logger = ConsoleLogger("SystemIssueNotification")

    try:
        from app.utils.email_notification_service import notify_system_issue

        console_logger.info(
            "Processing system issue notification request",
            {
                "issue_type": issue_type,
                "component": component,
                "severity": severity
            }
        )

        # Send system issue notification
        success = notify_system_issue(
            issue_type=issue_type,
            issue_message=issue_message,
            component=component,
            severity=severity,
            additional_context=additional_context or {},
            org_id="system"
        )

        if success:
            console_logger.info("System issue notification sent successfully")
            return {
                "success": True,
                "message": "System issue notification sent successfully",
                "issue_type": issue_type,
                "component": component,
                "severity": severity,
                "timestamp": str(datetime.now().isoformat())
            }
        else:
            console_logger.error("Failed to send system issue notification")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send system issue notification"
            )

    except HTTPException:
        raise
    except Exception as e:
        console_logger.error(f"Unexpected error in system issue notification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/test-system-monitoring")
async def test_system_monitoring():
    """
    Test comprehensive system monitoring by simulating various system issues

    This endpoint sends test notifications for different types of system issues
    to verify the monitoring system is working correctly.

    Returns:
        dict: Test results for different issue types
    """
    console_logger = ConsoleLogger("SystemMonitoringTest")

    try:
        from app.utils.email_notification_service import notify_system_issue

        console_logger.info("Testing comprehensive system monitoring")

        # Test different types of system issues
        test_issues = [
            {
                "issue_type": "database_error",
                "component": "database_connection",
                "severity": "CRITICAL",
                "message": "Test database connection failure"
            },
            {
                "issue_type": "api_error",
                "component": "gemini_api",
                "severity": "HIGH",
                "message": "Test Gemini API failure"
            },
            {
                "issue_type": "network_error",
                "component": "network_connectivity",
                "severity": "MEDIUM",
                "message": "Test network connectivity issue"
            }
        ]

        results = {}

        for test_issue in test_issues:
            try:
                success = notify_system_issue(
                    issue_type=test_issue["issue_type"],
                    issue_message=test_issue["message"],
                    component=test_issue["component"],
                    severity=test_issue["severity"],
                    additional_context={
                        "test_type": "system_monitoring_test",
                        "timestamp": str(datetime.now().isoformat()),
                        "endpoint": "/error-notification/test-system-monitoring"
                    },
                    org_id="system"
                )

                results[test_issue["issue_type"]] = {
                    "success": success,
                    "component": test_issue["component"],
                    "severity": test_issue["severity"]
                }

            except Exception as e:
                results[test_issue["issue_type"]] = {
                    "success": False,
                    "error": str(e),
                    "component": test_issue["component"],
                    "severity": test_issue["severity"]
                }

        console_logger.info("System monitoring test completed")

        return {
            "success": True,
            "message": "System monitoring test completed",
            "test_results": results,
            "timestamp": str(datetime.now().isoformat()),
            "notification_email": "<EMAIL>"
        }

    except Exception as e:
        console_logger.error(f"System monitoring test failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"System monitoring test failed: {str(e)}"
        )
