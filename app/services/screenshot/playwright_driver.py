#!/usr/bin/env python3
import asyncio
import argparse
import sys
import logging
import json
import os
import base64
import tempfile
import time
from playwright.async_api import async_playwright, Playwright
import app.services.screenshot.user_agents as ua

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    stream=sys.stdout
)

logger = logging.getLogger("screenshot_driver")

def normalize_instagram_url(url):
    """
    Normalize Instagram URLs to avoid login redirects
    """
    if "instagram.com" in url:
        # Remove any tracking parameters
        if "?" in url:
            url = url.split("?")[0]
        
        # Ensure URL ends with / for consistency
        if not url.endswith("/"):
            url += "/"
            
        # For specific post URLs, try to keep them as-is
        # For profile URLs, ensure they're properly formatted
        if "/p/" in url or "/reel/" in url or "/tv/" in url:
            # This is a specific content URL, keep as-is
            pass
        elif url.endswith("instagram.com/"):
            # Home page, keep as-is
            pass
        else:
            # This might be a profile URL, ensure proper format
            parts = url.split("/")
            if len(parts) >= 4 and parts[3]:  # Has username
                # Reconstruct URL properly
                username = parts[3]
                url = f"https://www.instagram.com/{username}/"
    
    return url

async def capture_instagram_screenshot_fallback(playwright: Playwright, url, timeout=60):
    """
    Fallback method for Instagram screenshots using a different approach
    """
    logger.info(f"Attempting Instagram fallback screenshot method for: {url}")
    
    # Try Firefox as an alternative to Chrome
    browser = await playwright.firefox.launch(
        headless=True,
        args=['--no-sandbox', '--disable-setuid-sandbox']
    )
    
    context = await browser.new_context(
        viewport={"width": 1366, "height": 768},
        user_agent= ua.get_random_user_agent(),
        extra_http_headers={
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1"
        }
    )
    
    page = await context.new_page()
    
    try:
        # Navigate with a shorter timeout and different strategy
        await page.goto(url, wait_until="load", timeout=timeout*1000)

        # Wait for any content to load
        await page.wait_for_timeout(3000)  # Increased from 2000 to 3000ms for better content loading

        # Try to dismiss any popups quickly
        try:
            await page.keyboard.press('Escape')
            await page.wait_for_timeout(1000)  # Increased from 500 to 1000ms
        except Exception:
            pass

        # Wait for images to load (Instagram-specific timing)
        logger.info("Waiting for Instagram images to load...")
        await page.wait_for_timeout(5000)  # 5 seconds for Instagram image loading

        # Try to wait for network to settle
        try:
            await page.wait_for_load_state("networkidle", timeout=3000)
            logger.info("Network settled for Instagram fallback")
        except Exception:
            logger.info("Network settle timeout in Instagram fallback (expected)")

        # Take screenshot
        screenshot_data = await page.screenshot(full_page=True, type="png")
        return screenshot_data

    except Exception as e:
        logger.warning(f"Firefox fallback failed: {str(e)}")
        return None
    finally:
        # Robust cleanup
        try:
            contexts = browser.contexts
            for context in contexts:
                try:
                    await context.close()
                except Exception:
                    pass
            await browser.close()
        except Exception:
            pass

async def capture_screenshot(playwright: Playwright, url, timeout=60, close_popups=True, wait_after_close=100, image_load_wait=3000):
    """
    Capture a screenshot of a URL using Playwright - OPTIMIZED VERSION

    Args:
        playwright (Playwright): The Playwright instance.
        url (str): The URL to capture a screenshot of
        timeout (int): Timeout in seconds (reduced from 90 to 30)
        close_popups (bool): Whether to attempt closing popups
        wait_after_close (int): Time in milliseconds to wait after closing popups (reduced from 500 to 100)
        image_load_wait (int): Time in milliseconds to wait for images to load before screenshot (default: 3000)

    Returns:
        bytes: Screenshot data as bytes
    """
    start_time = time.time()
    
    # Normalize Instagram URLs to avoid login redirects
    original_url = url
    url = normalize_instagram_url(url)
    if url != original_url:
        logger.info(f"Normalized URL: {original_url} -> {url}")
    
    logger.info(f"Starting OPTIMIZED screenshot capture for {url}")
    
    # Optimized browser launch with performance flags and resource limits
    browser = await playwright.chromium.launch(
        headless=True,
        args=[
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--disable-gpu',
            '--window-size=1920,1080',
            '--disable-features=TranslateUI',
            '--disable-renderer-backgrounding',
            '--disable-background-networking',
            '--disable-blink-features=AutomationControlled',
            '--disable-extensions',
            '--no-first-run',
            '--disable-default-apps',
            '--memory-pressure-off',
            '--max_old_space_size=2048',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-ipc-flooding-protection'
        ]
    )
    # Enhanced context for Instagram with better stealth settings
    context_options = {
        "viewport": {"width": 1920, "height": 1080},
        "user_agent": ua.get_random_user_agent(),
        "extra_http_headers": {
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "no-cache"
        }
    }
    
    # For Instagram, add some basic cookies to appear more like a real browser
    if "instagram.com" in url:
        context_options["extra_http_headers"]["Referer"] = "https://www.google.com/"
        context_options["extra_http_headers"]["Sec-Fetch-User"] = "?1"
    
    context = await browser.new_context(**context_options)
    page = await context.new_page()
    
    # Enhanced stealth scripts to avoid detection
    await page.add_init_script("""
        // Remove webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // Override the plugins property to use a custom getter
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // Override the languages property to use a custom getter
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
        });
        
        // Override the permissions property to use a custom getter
        Object.defineProperty(navigator, 'permissions', {
            get: () => ({
                query: () => Promise.resolve({ state: 'granted' }),
            }),
        });
        
        // Mock chrome runtime for better stealth
        window.chrome = {
            runtime: {}
        };
        
        // Override screen resolution
        Object.defineProperty(screen, 'width', {
            get: () => 1920,
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080,
        });
    """)
    
    try:
        # Set faster timeouts for the page
        page.set_default_timeout(timeout * 1000)
        page.set_default_navigation_timeout(timeout * 1000)
        
        # Navigate to the page with optimized wait strategy
        logger.info(f"Navigating to {url}")
        await page.goto(url, wait_until="domcontentloaded", timeout=timeout*5000)  # Changed from networkidle to domcontentloaded
        
        if close_popups:
            # Quick initial wait for popups to appear
            await asyncio.sleep(2)  # Reduced from 5 to 2 seconds
            
            # Try to press Escape key as it often closes modal popups on many sites
            try:
                await page.keyboard.press('Escape')
                logger.info("Pressed Escape key to try closing popup")
                await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
            except Exception as e:
                logger.warning(f"Error pressing Escape key: {str(e)}")

            # Platform-specific popup handling
            if "instagram.com" in url:
                logger.info("Handling Instagram-specific redirects and popups...")

                try:
                    # Strategy 1: Quick wait for initial page load
                    await page.wait_for_timeout(3000)  # Reduced from 6000 to 3000ms
                    
                    # Strategy 2: Check for login redirect and try multiple approaches
                    current_url = page.url
                    logger.info(f"Current URL after initial load: {current_url}")
                    
                    if "/accounts/login" in current_url or "/login" in current_url:
                        logger.info("Detected Instagram login page redirect...")
                        
                        # Approach 1: Try to navigate back to original URL with different wait strategy
                        logger.info("Trying to navigate back to original URL...")
                        try:
                            await page.goto(url, wait_until="domcontentloaded", timeout=15000)  # Reduced timeout
                            await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms
                            current_url = page.url
                            logger.info(f"URL after navigation attempt: {current_url}")
                        except Exception as e:
                            logger.warning(f"Navigation back failed: {str(e)}")
                        
                        # Approach 2: If still on login page, try with different URL format
                        if "/accounts/login" in current_url or "/login" in current_url:
                            logger.info("Still on login page, trying alternative URL approach...")
                            
                            # Try adding parameters to bypass login
                            if "?" not in url:
                                alternative_url = url + "?__a=1"
                                try:
                                    await page.goto(alternative_url, wait_until="domcontentloaded", timeout=10000)  # Reduced timeout
                                    await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms
                                    current_url = page.url
                                    logger.info(f"URL after alternative approach: {current_url}")
                                except Exception as e:
                                    logger.warning(f"Alternative URL approach failed: {str(e)}")
                            
                            # Try mobile version which sometimes works better
                            if "/accounts/login" in current_url or "/login" in current_url:
                                mobile_url = url.replace("www.instagram.com", "m.instagram.com")
                                if mobile_url != url:
                                    logger.info("Trying mobile version...")
                                    try:
                                        await page.goto(mobile_url, wait_until="domcontentloaded", timeout=10000)  # Reduced timeout
                                        await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms
                                        current_url = page.url
                                        logger.info(f"URL after mobile attempt: {current_url}")
                                    except Exception as e:
                                        logger.warning(f"Mobile version failed: {str(e)}")
                            
                            # Try a different approach - go to Instagram home first, then navigate
                            if "/accounts/login" in current_url or "/login" in current_url:
                                logger.info("Trying home-first approach...")
                                try:
                                    await page.goto("https://www.instagram.com/", wait_until="domcontentloaded", timeout=10000)  # Reduced timeout
                                    await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms

                                    # Try to dismiss any popups on home page
                                    try:
                                        await page.keyboard.press('Escape')
                                        await page.wait_for_timeout(500)  # Reduced from 1000 to 500ms
                                    except Exception:
                                        pass

                                    # Now try to navigate to the target URL
                                    if url != "https://www.instagram.com/":
                                        await page.goto(url, wait_until="domcontentloaded", timeout=10000)  # Reduced timeout
                                        await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms
                                        current_url = page.url
                                        logger.info(f"URL after home-first attempt: {current_url}")
                                except Exception as e:
                                    logger.warning(f"Home-first approach failed: {str(e)}")
                    
                    # Strategy 3: Handle any popups that might appear
                    await page.wait_for_timeout(500)  # Reduced from 1000 to 500ms
                    
                    # Handle cookie consent banner
                    cookie_selectors = [
                        'button:has-text("Accept")',
                        'button:has-text("Allow all cookies")',
                        'button[data-cookiebanner="accept_button"]',
                        'button:has-text("Accept All")'
                    ]
                    
                    for selector in cookie_selectors:
                        try:
                            cookie_btn = page.locator(selector)
                            if await cookie_btn.count() > 0:
                                await cookie_btn.first.click()
                                logger.info(f"Accepted cookies with selector: {selector}")
                                await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                                break
                        except Exception:
                            continue
                    
                    # Handle notification popups
                    notification_selectors = [
                        'button:has-text("Not Now")',
                        'button:has-text("Not now")',
                        'button:has-text("Maybe Later")',
                        'button[data-testid="turnOnNotifications"]'
                    ]
                    
                    for selector in notification_selectors:
                        try:
                            notif_btn = page.locator(selector)
                            if await notif_btn.count() > 0:
                                await notif_btn.first.click()
                                logger.info(f"Dismissed notification with selector: {selector}")
                                await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                                break
                        except Exception:
                            continue
                    
                    # Handle login/signup dialog overlays
                    close_selectors = [
                        'svg[aria-label="Close"][role="img"]',
                        'button[aria-label="Close"]',
                        'div[role="button"][aria-label="Close"]',
                        'button:has-text("Close")',
                        '[data-testid="loginForm"] button[type="button"]'
                    ]
                    
                    for selector in close_selectors:
                        try:
                            close_btn = page.locator(selector)
                            if await close_btn.count() > 0:
                                if selector == 'svg[aria-label="Close"][role="img"]':
                                    # For SVG icons, get the parent button
                                    button = close_btn.locator('xpath=ancestor::button').first
                                    await button.click()
                                else:
                                    await close_btn.first.click()
                                logger.info(f"Closed dialog with selector: {selector}")
                                await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                                break
                        except Exception:
                            continue
                    
                    # Try pressing Escape key multiple times
                    for _ in range(3):
                        try:
                            await page.keyboard.press('Escape')
                            await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                        except Exception:
                            break
                    
                    # Strategy 4: Handle any remaining modal dialogs
                    modal_dialogs = page.locator('div[role="dialog"]')
                    if await modal_dialogs.count() > 0:
                        logger.info("Found modal dialogs, attempting to close...")
                        
                        # Try clicking outside all modals
                        try:
                            await page.click('body', position={'x': 50, 'y': 50})
                            await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                        except Exception:
                            pass

                        # Try clicking on backdrop
                        try:
                            await page.click('div[role="dialog"]', position={'x': 10, 'y': 10})
                            await page.wait_for_timeout(200)  # Reduced from 500 to 200ms
                        except Exception:
                            pass
                    
                    # Strategy 5: Final URL check and content optimization
                    final_url = page.url
                    logger.info(f"Final URL: {final_url}")
                    
                    if "/accounts/login" in final_url or "/login" in final_url:
                        logger.warning("Still on Instagram login page after all attempts")
                        # Try to scroll to see if there's any content beyond the login form
                        try:
                            await page.mouse.wheel(0, 500)
                            await page.wait_for_timeout(500)  # Reduced from 1000 to 500ms
                            await page.mouse.wheel(0, -500)
                            await page.wait_for_timeout(500)  # Reduced from 1000 to 500ms
                        except Exception:
                            pass
                    else:
                        logger.info("Successfully navigated past Instagram login requirements")
                        # Wait for content to load
                        await page.wait_for_timeout(1000)  # Reduced from 2000 to 1000ms
                    
                except Exception as e:
                    logger.warning(f"Error handling Instagram: {str(e)}")
            
            elif "facebook.com" in url:
                logger.info("Handling Facebook-specific popups...")
                
                try:
                    # Close cookie consent/login popup
                    cookie_buttons = page.locator('button[data-cookiebanner="accept_button"], '
                                              'button[data-testid="cookie-policy-manage-dialog-accept-button"]')
                    if await cookie_buttons.count() > 0:
                        await cookie_buttons.first.click()
                        logger.info("Clicked cookie consent button")
                        await page.wait_for_timeout(50)  # Reduced from 100 to 50ms

                    # Close login dialog
                    login_close = page.locator('[aria-label="Close"]')
                    if await login_close.count() > 0:
                        await login_close.first.click()
                        logger.info("Closed Facebook login dialog")
                        await page.wait_for_timeout(50)  # Reduced from 100 to 50ms
                except Exception as e:
                    logger.warning(f"Error handling Facebook popup: {str(e)}")
            
            elif any(x_domain in url for x_domain in ["twitter.com", "x.com"]):
                logger.info("Handling X/Twitter-specific popups...")
                
                try:
                    # Handle the login wall popup
                    login_dialog_close = page.locator('div[aria-modal="true"] div[role="button"][data-testid="close"]')
                    if await login_dialog_close.count() > 0:
                        await login_dialog_close.click()
                        logger.info("Closed X/Twitter login dialog")
                        await page.wait_for_timeout(200)  # Reduced from 400 to 200ms

                    # Handle cookie consent
                    cookie_buttons = page.locator('div[role="dialog"] button:has-text("Accept")')
                    if await cookie_buttons.count() > 0:
                        await cookie_buttons.first.click()
                        logger.info("Clicked X/Twitter cookie accept button")
                        await page.wait_for_timeout(150)  # Reduced from 300 to 150ms
                except Exception as e:
                    logger.warning(f"Error handling X/Twitter popup: {str(e)}")
            
            elif "linkedin.com" in url:
                logger.info("Handling LinkedIn-specific popups...")
                
                try:
                    # Close signin popup
                    dismiss_buttons = page.locator('button.artdeco-modal__dismiss, button[aria-label="Dismiss"]')
                    if await dismiss_buttons.count() > 0:
                        await dismiss_buttons.first.click()
                        logger.info("Dismissed LinkedIn popup")
                        await page.wait_for_timeout(150)  # Reduced from 300 to 150ms

                    # Cookie consent banner
                    cookie_buttons = page.locator('button[action-type="DENY"], button[data-control-name="ga-cookie.consent.reject.v4"]')
                    if await cookie_buttons.count() > 0:
                        await cookie_buttons.first.click()
                        logger.info("Rejected LinkedIn cookies")
                        await page.wait_for_timeout(150)  # Reduced from 300 to 150ms
                        
                    # Login wall
                    try:
                        # The main page scroll might still work to see content even with login wall
                        await page.mouse.wheel(0, 500)
                        logger.info("Scrolled down to potentially see content behind login wall")
                    except Exception:
                        pass
                except Exception as e:
                    logger.warning(f"Error handling LinkedIn popup: {str(e)}")
            
            # Generic popup handling for all platforms
            try:
                # Try to find and handle any common dialog popups that weren't caught by platform-specific handlers
                
                # Method 1: Look for elements with role="dialog" and find close buttons within them
                dialog = page.locator('div[role="dialog"]')
                if await dialog.count() > 0:
                    logger.info("Found a dialog popup, attempting to close it...")
                    
                    # Try to find close buttons with various common patterns
                    close_buttons = [
                        # SVG close buttons (common in modern sites)
                        'svg[aria-label="Close"], svg[aria-label="close"], svg[aria-label="Dismiss"]',
                        # X text buttons 
                        'button:has-text("✕"), button:has-text("×"), button:has-text("X")',
                        # Close/Cancel text buttons
                        'button:has-text("Close"), button:has-text("Cancel"), button:has-text("Dismiss")',
                        # Aria labeled buttons
                        'button[aria-label="Close"], button[aria-label="Dismiss"], button[aria-label="Cancel"]',
                        # Modal dismiss buttons
                        '.modal-close, .modal-dismiss, .close-button'
                    ]
                    
                    # Try each selector
                    for selector in close_buttons:
                        button = page.locator(selector)
                        if await button.count() > 0:
                            await button.first.click()
                            logger.info(f"Closed dialog with selector: {selector}")
                            await page.wait_for_timeout(wait_after_close)
                            break
                
                # Method 2: Try clicking the backdrop/overlay (often closes modals)
                overlays = page.locator('.modal-backdrop, .modal-overlay, .overlay, .dialog-overlay')
                if await overlays.count() > 0:
                    try:
                        await overlays.first.click(position={x: 10, y: 10})  # Click near the edge
                        logger.info("Clicked on overlay/backdrop to attempt closing popup")
                        await page.wait_for_timeout(wait_after_close)
                    except:
                        pass
                
                # Method 3: Try a few more generic approaches if still needed
                generic_buttons = page.locator('button, a[role="button"]')
                for i in range(min(await generic_buttons.count(), 5)):  # Check only first few buttons
                    try:
                        button = generic_buttons.nth(i)
                        button_text = await button.text_content() or ""
                        button_aria = await button.get_attribute("aria-label") or ""
                        
                        # Check if this looks like a close button
                        if any(x in button_text.lower() for x in ["close", "dismiss", "cancel", "✕", "×", "x"]) or \
                           any(x in button_aria.lower() for x in ["close", "dismiss", "cancel"]):
                            await button.click()
                            logger.info(f"Clicked potential close button: {button_text or button_aria}")
                            await page.wait_for_timeout(wait_after_close)
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.warning(f"Error while trying generic popup handling: {str(e)}")

        # Wait for images to load before taking screenshot
        logger.info(f"Waiting {image_load_wait}ms for images to load...")
        await page.wait_for_timeout(image_load_wait)

        # Additional wait for network activity to settle (especially for social media)
        url_lower = url.lower()
        if any(platform in url_lower for platform in ["instagram.com", "facebook.com", "twitter.com", "x.com", "linkedin.com", "youtube.com", "pinterest.com"]):
            logger.info("Social media platform detected - waiting for network to settle...")
            try:
                # Wait for network to be mostly idle (allows some ongoing requests)
                await page.wait_for_load_state("networkidle", timeout=5000)
                logger.info("Network settled for social media platform")
            except Exception as e:
                logger.info(f"Network settle timeout (expected for some platforms): {str(e)}")

        # Take a screenshot with optimization
        logger.info("Taking optimized screenshot")
        screenshot_bytes = await page.screenshot(
            timeout=10000,  # 10s timeout for screenshot
            # quality=85,     # Slightly compressed for speed
            type='png',
            full_page=True      # Removed full_page=True for better performance
        )
        
        capture_time = time.time() - start_time
        logger.info(f"Screenshot captured in {capture_time:.2f}s, size: {len(screenshot_bytes)} bytes")
        
        return screenshot_bytes
        
    except Exception as e:
        logger.error(f"Error capturing screenshot: {str(e)}")
        
        # If this is an Instagram URL and main method failed, try fallback
        if "instagram.com" in url:
            logger.info("Main method failed for Instagram, attempting fallback method...")
            try:
                await browser.close()
                fallback_result = await capture_instagram_screenshot_fallback(playwright, url, timeout=30)
                if fallback_result:
                    logger.info("Instagram fallback method succeeded!")
                    return fallback_result
                else:
                    logger.warning("Instagram fallback method also failed")
            except Exception as fallback_error:
                logger.error(f"Instagram fallback error: {str(fallback_error)}")
        
        return None
    finally:
        # Robust cleanup to prevent zombie processes
        try:
            if browser:
                logger.info("Starting browser cleanup process")

                # Check if browser is still connected
                try:
                    is_connected = browser.is_connected()
                    logger.debug(f"Browser connection status: {is_connected}")
                except Exception:
                    is_connected = False

                if is_connected:
                    # Close all contexts first
                    try:
                        contexts = browser.contexts
                        logger.debug(f"Closing {len(contexts)} browser contexts")
                        for context in contexts:
                            try:
                                await asyncio.wait_for(context.close(), timeout=5.0)
                            except Exception as e:
                                logger.warning(f"Error closing context: {e}")
                    except Exception as e:
                        logger.warning(f"Error accessing browser contexts: {e}")

                    # Close the browser with timeout
                    try:
                        await asyncio.wait_for(browser.close(), timeout=10.0)
                        logger.info("Browser closed successfully")
                    except asyncio.TimeoutError:
                        logger.warning("Browser close timed out, forcing cleanup")
                        # Force kill browser process if normal close fails
                        try:
                            if hasattr(browser, '_connection') and browser._connection:
                                await browser._connection.dispose()
                        except Exception as force_error:
                            logger.warning(f"Error during force browser cleanup: {force_error}")
                else:
                    logger.info("Browser was already disconnected")

        except Exception as e:
            logger.error(f"Error during browser cleanup: {e}")
            # Additional cleanup attempt
            try:
                import psutil
                import os
                # Kill any remaining chromium processes
                for proc in psutil.process_iter(['pid', 'name']):
                    if 'chromium' in proc.info['name'].lower() or 'chrome' in proc.info['name'].lower():
                        try:
                            proc.kill()
                            logger.debug(f"Killed chromium process: {proc.info['pid']}")
                        except Exception:
                            pass
            except Exception:
                pass