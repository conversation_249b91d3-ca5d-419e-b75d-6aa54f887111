"""
Wrapper module for screenshot capture functionality
Provides a simplified interface for capturing screenshots without requiring direct playwright management
"""
import async<PERSON>
from typing import Op<PERSON>
from playwright.async_api import async_playwright
from app.services.screenshot.playwright_driver import capture_screenshot as _capture_screenshot_with_playwright
from app.utils.logger import ConsoleLogger

logger = ConsoleLogger("run_screenshot")

async def capture_screenshot(url: str, timeout: int = 60, close_popups: bool = None, wait_after_close: int = 100, image_load_wait: int = None) -> Optional[bytes]:
    """
    Capture a screenshot of a URL using Playwright

    This is a wrapper function that manages the playwright instance internally,
    providing a simpler interface for screenshot capture.

    Args:
        url (str): The URL to capture a screenshot of
        timeout (int): Timeout in seconds (default: 60)
        close_popups (bool): Whether to attempt closing popups (default: auto-detect based on URL)
        wait_after_close (int): Time in milliseconds to wait after closing popups (default: 100)
        image_load_wait (int): Time in milliseconds to wait for images to load before screenshot (default: auto-detect based on URL)

    Returns:
        bytes: Screenshot data as bytes, or None if capture failed
    """
    try:
        # Auto-detect if we should close popups based on URL type
        if close_popups is None:
            # Only enable popup closing for platforms that actually have problematic popups
            platforms_with_popups = [
                "instagram.com",     # Has login/signup popups
                "facebook.com",      # Has login/cookie popups
                "fb.com",           # Facebook short domain
                "twitter.com",      # Has login wall popups
                "x.com",            # Twitter rebrand
                "pinterest.com",    # Has signup popups
                "pin.it"           # Pinterest short domain
                
            ]
            close_popups = any(pattern in url.lower() for pattern in platforms_with_popups)
            logger.info(f"Auto-detected popup setting for {url}: close_popups={close_popups} (platforms with popups: {platforms_with_popups})")

        # Auto-detect image loading wait time based on URL type
        if image_load_wait is None:
            # Platform-specific image loading wait times (in milliseconds)
            social_media_platforms = {
                "instagram.com": 5000,    # Instagram needs more time for image loading
                "facebook.com": 4000,     # Facebook has complex image loading
                "fb.com": 4000,          # Facebook short domain
                "twitter.com": 3500,     # Twitter/X has dynamic image loading
                "x.com": 3500,           # Twitter rebrand
                "linkedin.com": 4000,    # LinkedIn has professional images
                "youtube.com": 4500,     # YouTube thumbnails and video previews
                "youtu.be": 4500,        # YouTube short domain
                "pinterest.com": 5000,   # Pinterest is image-heavy
                "pin.it": 5000,          # Pinterest short domain
                "tiktok.com": 4000,      # TikTok video thumbnails
            }

            # Check if URL matches any social media platform
            url_lower = url.lower()
            image_load_wait = 3000  # Default 3 seconds for regular websites

            for platform, wait_time in social_media_platforms.items():
                if platform in url_lower:
                    image_load_wait = wait_time
                    break

            logger.info(f"Auto-detected image loading wait time for {url}: {image_load_wait}ms")

        async with async_playwright() as playwright:
            return await _capture_screenshot_with_playwright(
                playwright=playwright,
                url=url,
                timeout=timeout,
                close_popups=close_popups,
                wait_after_close=wait_after_close,
                image_load_wait=image_load_wait
            )
    except Exception as e:
        logger.error(f"Error in capture_screenshot wrapper for {url}", error=e)
        return None
