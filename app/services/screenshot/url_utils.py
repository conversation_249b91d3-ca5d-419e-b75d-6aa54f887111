import json
import os
import random
import re
import time
import asyncio
from base64 import b64encode
from datetime import datetime
from mimetypes import guess_type
from urllib.parse import urlparse

import requests
from playwright.async_api import async_playwright

from app.config import settings
from app.utils import logger
from app.utils.logger import ConsoleLogger
from app.services.screenshot.user_agents import USER_AGENTS

# Placeholder functions for proxy counter
def increment_direct_attempt(domain, endpoint_type, org_id):
    pass

def increment_direct_success(domain, endpoint_type, org_id):
    pass

def increment_proxy_attempt(domain, endpoint_type, org_id):
    pass

def increment_proxy_success(domain, endpoint_type, org_id):
    pass

admin_username = settings.ADMIN_USERNAME
admin_password = settings.ADMIN_PASSWORD
base_url = settings.BASE_URL

# Proxy usage counter
PROXY_COUNTER_FILE = os.path.join(os.path.dirname(__file__), "..", "..", "proxy_usage_stats.json")

# Initialize proxy usage stats
proxy_usage_stats = {
    "direct_connection_attempts": 0,
    "direct_connection_successes": 0,
    "proxy_connection_attempts": 0,
    "proxy_connection_successes": 0,
    "last_updated": datetime.now().isoformat(),
}

# Default timeout values (in seconds) - REDUCED for better responsiveness
DEFAULT_TOTAL_TIMEOUT = 90  # 1.5 minutes total timeout for the entire process
DEFAULT_TEXT_EXTRACTION_TIMEOUT = 60  # 1 minute for text extraction
DEFAULT_SCREENSHOT_TIMEOUT = 60  # 1 minute for screenshot capture

# Load existing stats if available
def load_proxy_stats():
    global proxy_usage_stats
    try:
        if os.path.exists(PROXY_COUNTER_FILE):
            with open(PROXY_COUNTER_FILE, "r") as f:
                proxy_usage_stats = json.load(f)
    except Exception as e:
        logger = ConsoleLogger("proxy_stats")
        logger.error(f"Error loading proxy stats: {str(e)}")


# Save stats to file
def save_proxy_stats():
    try:
        proxy_usage_stats["last_updated"] = datetime.now().isoformat()
        with open(PROXY_COUNTER_FILE, "w") as f:
            json.dump(proxy_usage_stats, f, indent=2)
    except Exception as e:
        logger = ConsoleLogger("proxy_stats")
        logger.error(f"Error saving proxy stats: {str(e)}")


# Initialize by loading existing stats
load_proxy_stats()


def get_domain_without_prefix(url: str) -> str:
    try:
        parsed_url = urlparse(url)
        domain = parsed_url.netloc
        if not domain:
            domain = parsed_url.path.split("/")[0]
        domain = domain.lstrip("www.")
        return domain
    except ValueError:
        return None


def strip_url(url):
    try:
        parsed_url = urlparse(url)
        stripped_url = parsed_url.netloc
        if not stripped_url:
            stripped_url = parsed_url.path
        else:
            stripped_url += parsed_url.path

        stripped_url = stripped_url.lstrip("www.")
        stripped_url = stripped_url.strip("/")
        stripped_url = stripped_url.replace("/", "_")
        return stripped_url
    except ValueError:
        return None


def get_text_from_url(url, logger: ConsoleLogger):
    auth_string = f"{admin_username}:{admin_password}"
    encoded_auth = b64encode(auth_string.encode()).decode()
    headers = {"Authorization": f"Basic {encoded_auth}"}

    url = f"{base_url}/api/admin/page/text?url={url}"

    for attempt in range(3):  # Retry up to 3 times
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            url_text = response.text
            return url_text
        elif attempt < 2:  # Log retry attempt if not the last one
            logger.warning(
                "Failed to get text from URL, retrying",
                {"attempt": attempt + 1, "status_code": response.status_code},
            )
            # Use exponential backoff: 1s, 2s for retries
            time.sleep(attempt + 1)

    raise Exception(
        f"get_text_from_url: Request failed after 3 attempts: {response.status_code} - {response.text}"
    )


def check_social_media_login_redirect(url, final_url, page_title, page_content):
    """
    Check if a URL has been redirected to a social media login page
    
    Args:
        url (str): Original URL
        final_url (str): Final URL after redirects
        page_title (str): Page title
        page_content (str): Page content
        
    Returns:
        bool: True if redirected to login page
    """
    # Social media login redirect indicators
    social_media_login_indicators = [
        "/accounts/login/",
        "/login/",
        "/uas/login",
        "/i/flow/login",
        "log in to facebook",
        "log into facebook", 
        "log in to instagram",
        "log into instagram",
        "sign in to linkedin",
        "sign in to x",
        "sign in to twitter",
        "log in to twitter",
        "you must log in",
        "login required",
        "sign in required",
        "authentication required",
        "login_form",
        "signin_form",
        "loginForm",
        "authwall",
        "login-page",
        "signin-page"
    ]
    
    final_url_lower = final_url.lower()
    page_title_lower = page_title.lower()
    page_content_lower = page_content.lower()
    
    for indicator in social_media_login_indicators:
        if indicator in final_url_lower or indicator in page_title_lower or indicator in page_content_lower:
            return True
    
    return False


async def get_text_from_url_local(url, endpoint_type="general", org_id=None, timeout=DEFAULT_TEXT_EXTRACTION_TIMEOUT):
    """
    Extract text from a URL with a global timeout.
    
    Args:
        url (str): The URL to extract text from
        endpoint_type (str): The type of endpoint making the request
        org_id (str, optional): Organization ID for tracking
        timeout (int, optional): Total timeout in seconds for the entire process
        
    Returns:
        str: Extracted text or empty string on failure/timeout
    """
    logger = ConsoleLogger("text_extraction")
    logger.info("Starting text extraction", {"url": url, "timeout": timeout})

    # List of common blocking indicators in page content
    blocking_indicators = [
        "access denied",
        "403 forbidden",
        "blocked",
        "your ip has been blocked",
        "access restricted",
        "geographic restriction",
        "not available in your region",
        "not available in your country",
        "this content is not available",
        "cloudflare",
        "captcha",
        "robot check",
        "security check",
        "please verify you are a human",
        "ddos protection",
        "automated access",
        "bot detection",
        "suspicious activity",
        "unusual traffic",
        "rate limited",
        "too many requests",
    ]

    # Add timeout constants - REDUCED for better responsiveness
    NAVIGATION_TIMEOUT = 30000  # 30 seconds in milliseconds (reduced from 60s for faster failures)
    WAIT_FOR_LOAD_TIMEOUT = 20000  # 20 seconds in milliseconds (reduced from 45s for faster failures)
    PAGE_TIMEOUT = 60000  # 60 seconds in milliseconds (reduced from 120s for faster failures)

    async def check_for_blocking(page):
        """Check if the page appears to be showing a blocking message"""
        try:
            # Get page title
            title = await page.title()
            # Get visible text on the page
            body_text = await page.evaluate("() => document.body.innerText")

            # Check title and body text for blocking indicators
            title_lower = title.lower()
            body_lower = body_text.lower()

            for indicator in blocking_indicators:
                if indicator in title_lower or indicator in body_lower:
                    logger.warning(
                        "Blocking content detected on page", {"url": url, "indicator": indicator}
                    )
                    return True

            # Check if page has very little content (might be a simple block page)
            if len(body_text.strip()) < 100 and ("error" in body_lower or "sorry" in body_lower):
                logger.warning(
                    "Suspiciously small page content detected",
                    {"url": url, "content_length": len(body_text)},
                )
                return True

            # Check for JavaScript challenges
            js_challenge_indicators = await page.evaluate(
                """() => {
                const scripts = Array.from(document.querySelectorAll('script'));
                for (const script of scripts) {
                    if (script.textContent && (
                        script.textContent.includes('challenge') ||
                        script.textContent.includes('security') ||
                        script.textContent.includes('captcha') ||
                        script.textContent.includes('cloudflare')
                    )) {
                        return true;
                    }
                }
                return false;
            }"""
            )

            if js_challenge_indicators:
                logger.warning("JavaScript security challenge detected", {"url": url})
                return True

            return False
        except Exception as e:
            logger.error("Error checking for blocking content", {"error": str(e)})
            return False

    async def setup_stealth_browser(playwright, use_proxy=False):
        """Set up a browser with stealth features to avoid detection"""
        # Select a random user agent
        user_agent = random.choice(USER_AGENTS)

        # Browser launch options
        browser_options = {
            "headless": True,
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-features=IsolateOrigins,site-per-process",
                "--disable-web-security",
                "--disable-site-isolation-trials",
            ],
        }

        # Add proxy if requested and properly configured
        if use_proxy:
            # Try to get proxy credentials from environment
            import os
            proxy_username = os.getenv("WEBSHARE_PROXY_USERNAME", "")
            proxy_password = os.getenv("WEBSHARE_PROXY_PASSWORD", "")

            # Validate proxy credentials (allow admin/admin for webshare, block other placeholders)
            valid_credentials = (
                proxy_username and proxy_password and
                proxy_username.strip() and proxy_password.strip() and
                not (proxy_username.lower() in ["test", "placeholder"] or
                     proxy_password.lower() in ["test", "placeholder"])
            )

            if valid_credentials:
                proxy_config = {
                    "server": "p.webshare.io:9999",
                    "username": proxy_username,
                    "password": proxy_password
                }
                browser_options["proxy"] = proxy_config
                logger.info("Using webshare proxy with valid credentials")
            else:
                logger.warning(
                    "Webshare proxy requested but credentials are invalid or placeholder values. "
                    "Falling back to direct connection.",
                    {"username_provided": bool(proxy_username), "password_provided": bool(proxy_password)}
                )
                # Don't use proxy if credentials are invalid
                use_proxy = False

        # Launch the browser
        browser = await playwright.chromium.launch(**browser_options)

        # Create a new browser context with the stealth settings
        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent=user_agent,
            is_mobile=False,
            has_touch=False,
            locale="en-US",
            timezone_id="America/New_York",
            permissions=["geolocation"],
            bypass_csp=True,
        )

        # Add JavaScript to evade detection
        await context.add_init_script(
            """
            () => {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });
                
                window.chrome = {
                    runtime: {},
                };
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {
                                type: "application/x-google-chrome-pdf",
                                suffixes: "pdf",
                                description: "Portable Document Format",
                                enabledPlugin: Plugin
                            },
                            description: "Chrome PDF Plugin",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        }
                    ],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            }
            """
        )

        # Create a new page
        page = await context.new_page()

        # Additional page stealth settings
        await page.evaluate(
            """
            () => {
                history.pushState = history.pushState || function () {};
                history.replaceState = history.replaceState || function () {};
                window.getComputedStyle = window.getComputedStyle || function () {};
            }
            """
        )

        return browser, page

    async def try_direct_connection(playwright, current_url, current_logger, max_retries=1):
        """Try direct connection to URL"""
        # Record direct connection attempt
        increment_direct_attempt(get_domain_without_prefix(current_url), endpoint_type, org_id)
        
        browser = None
        page = None
        success = False

        for retry in range(max_retries + 1):
            try:
                browser, page = await setup_stealth_browser(playwright)
                await page.goto(current_url, timeout=NAVIGATION_TIMEOUT)
                
                # Use a more flexible wait strategy - try networkidle first, fall back to domcontentloaded
                try:
                    await page.wait_for_load_state("networkidle", timeout=WAIT_FOR_LOAD_TIMEOUT)
                except Exception as wait_error:
                    current_logger.warning(
                        "Networkidle timeout, falling back to domcontentloaded",
                        {"url": current_url, "retry": retry, "wait_error": str(wait_error)}
                    )
                    try:
                        await page.wait_for_load_state("domcontentloaded", timeout=10000)  # 10s fallback
                    except Exception as fallback_error:
                        current_logger.warning(
                            "Domcontentloaded timeout, proceeding anyway",
                            {"url": current_url, "retry": retry, "fallback_error": str(fallback_error)}
                        )
                
                # Check if page appears blocked
                is_blocked = await check_for_blocking(page)
                if is_blocked:
                    current_logger.warning(
                        "Page appears to be blocked with direct connection", {"url": current_url, "retry": retry}
                    )
                    if browser:
                        await browser.close()
                    browser = None
                    if retry < max_retries:
                        # Use exponential backoff for retries
                        delay = (2 ** retry) * 1.5
                        current_logger.info(f"Retrying direct connection in {delay:.2f} seconds")
                        await asyncio.sleep(delay)
                        continue
                    return None, None, False

                # Check for social media login redirects
                final_url = page.url
                page_title = await page.title()
                body_text_for_check = await page.evaluate("() => document.body.innerText")
                
                if check_social_media_login_redirect(current_url, final_url, page_title, body_text_for_check):
                    current_logger.warning(
                        "Social media login redirect detected - treating as blocked",
                        {"original_url": current_url, "final_url": final_url}
                    )
                    if browser:
                        await browser.close()
                    browser = None
                    return None, None, False
                
                # Extract text from the page
                body_text = await page.evaluate("() => document.body.innerText")
                html_content = await page.content()
                
                # Basic text cleaning
                body_text = re.sub(r'\s+', ' ', body_text).strip()
                
                # Record direct connection success
                increment_direct_success(get_domain_without_prefix(current_url), endpoint_type, org_id)
                
                current_logger.info(
                    "Text extraction successful with direct connection",
                    {
                        "url": current_url,
                        "text_length": len(body_text),
                        "direct_success": "true",
                    },
                )
                
                success = True
                return body_text, html_content, success
                
            except Exception as e:
                error_type = type(e).__name__
                error_message = str(e)
                
                # Provide specific handling for different error types
                if "TimeoutError" in error_type or "timeout" in error_message.lower():
                    current_logger.error(
                        "Failed to extract text with direct connection",
                        {"error": {"error": error_message, "url": current_url, "retry": retry}},
                    )
                else:
                    current_logger.error(
                        "Failed to extract text with direct connection",
                        {"error": error_message, "url": current_url, "retry": retry, "error_type": error_type},
                    )
                
                if browser:
                    try:
                        await browser.close()
                    except:
                        pass
                    browser = None
                
                if retry < max_retries:
                    # Use exponential backoff for retries
                    delay = (2 ** retry) * 1.5
                    current_logger.info(f"Retrying direct connection in {delay:.2f} seconds")
                    await asyncio.sleep(delay)
                else:
                    return None, None, False
            finally:
                if browser and success:
                    await browser.close()

    async def try_proxy_connection(playwright, current_url, current_logger):
        """Try to connect via proxy as a fallback"""
        # Increment proxy connection attempt counter
        increment_proxy_attempt(get_domain_without_prefix(current_url), endpoint_type, org_id)

        current_logger.info("Attempting proxy connection for text extraction as fallback")

        # Check if webshare is properly configured before attempting
        import os
        proxy_username = os.getenv("WEBSHARE_PROXY_USERNAME", "")
        proxy_password = os.getenv("WEBSHARE_PROXY_PASSWORD", "")

        valid_credentials = (
            proxy_username and proxy_password and
            proxy_username.strip() and proxy_password.strip() and
            not (proxy_username.lower() in ["test", "placeholder"] or
                 proxy_password.lower() in ["test", "placeholder"])
        )

        if not valid_credentials:
            current_logger.warning(
                "Proxy connection skipped - webshare credentials are invalid or placeholder values",
                {"url": current_url, "username_provided": bool(proxy_username)}
            )
            return None, None, False

        browser, page = await setup_stealth_browser(playwright, use_proxy=True)

        # Use a slightly longer timeout for proxy connections and better wait strategy
        try:
            await page.goto(current_url, timeout=NAVIGATION_TIMEOUT * 1.2)  # Reduced multiplier from 1.5 to 1.2
            
            # Use flexible wait strategy for proxy connections too
            try:
                await page.wait_for_load_state("networkidle", timeout=WAIT_FOR_LOAD_TIMEOUT)
            except Exception as wait_error:
                current_logger.warning(
                    "Proxy: Networkidle timeout, falling back to domcontentloaded",
                    {"url": current_url, "wait_error": str(wait_error)}
                )
                try:
                    await page.wait_for_load_state("domcontentloaded", timeout=10000)
                except Exception as fallback_error:
                    current_logger.warning(
                        "Proxy: Domcontentloaded timeout, proceeding anyway",
                        {"url": current_url, "fallback_error": str(fallback_error)}
                    )
            
            # Check if the page still shows blocking content with proxy
            is_blocked = await check_for_blocking(page)
            if is_blocked:
                current_logger.warning("Page appears to be blocked even with proxy connection")
                await browser.close()
                return None, None, False
            
            # Increment proxy connection success counter
            increment_proxy_success(get_domain_without_prefix(current_url), endpoint_type, org_id)
            
            current_logger.info(
                "Text extraction successful with proxy",
                {
                    "url": current_url,
                    "proxy_success": "true",
                    "text_length": len(body_text) if body_text else 0,
                },
            )
            
            # Extract text and html
            body_text = await page.evaluate("() => document.body.innerText")
            html_content = await page.content()
            
            # Basic text cleaning
            body_text = re.sub(r'\s+', ' ', body_text).strip()
            
            await browser.close()
            return body_text, html_content, True
            
        except Exception as e:
            error_type = type(e).__name__
            error_message = str(e)
            
            # Provide specific handling for different error types
            if "TimeoutError" in error_type or "timeout" in error_message.lower():
                current_logger.error(
                    "Failed to navigate to URL with proxy for text extraction (timeout)",
                    {"error": {"error": error_message, "url": current_url, "retry": 0}}
                )
            else:
                current_logger.error(
                    "Failed to navigate to URL with proxy for text extraction", 
                    {"error": error_message, "url": current_url, "error_type": error_type}
                )
            
            try:
                await browser.close()
            except:
                pass
            return None, None, False

    # The main extraction workflow with timeout
    async def _extraction_workflow():
        try:
            import playwright.async_api
            p = await playwright.async_api.async_playwright().start()
            
            try:
                # First try direct connection
                extracted_text, html_content, success = await try_direct_connection(p, url, logger, max_retries=1)
                
                # If direct connection failed, try proxy
                if not success:
                    extracted_text, html_content, success = await try_proxy_connection(p, url, logger)
                    
                if not success:
                    logger.error(
                        f"Failed to access {url} for text extraction with both direct and proxy connections"
                    )
                    return ""
                
                return extracted_text
            finally:
                await p.stop()
                
        except Exception as e:
            logger.error(f"Error in text extraction: {str(e)}")
            return ""

    try:
        # Apply global timeout to the entire process
        return await asyncio.wait_for(_extraction_workflow(), timeout=timeout)
    except asyncio.TimeoutError:
        logger.error(f"Text extraction timed out after {timeout} seconds for URL: {url}")
        return ""
    except Exception as e:
        logger.error(f"Unexpected error in text extraction process: {str(e)}")
        return ""


async def capture_screenshot_playwright(url, endpoint_type="general", org_id=None, timeout=DEFAULT_SCREENSHOT_TIMEOUT):
    """
    Capture a screenshot of a URL with a global timeout.
    
    Args:
        url (str): The URL to take a screenshot of
        endpoint_type (str): The type of endpoint making the request
        org_id (str, optional): Organization ID for tracking
        timeout (int, optional): Total timeout in seconds for the entire process
        
    Returns:
        str: Data URL containing the screenshot or None on failure/timeout
    """
    logger = ConsoleLogger("screenshot")
    logger.info("Starting screenshot capture", {"url": url, "timeout": timeout})

    # List of common blocking indicators in page content
    blocking_indicators = [
        "access denied",
        "403 forbidden",
        "blocked",
        "your ip has been blocked",
        "access restricted",
        "geographic restriction",
        "not available in your region",
        "not available in your country",
        "this content is not available",
        "cloudflare",
        "captcha",
        "robot check",
        "security check",
        "please verify you are a human",
        "ddos protection",
        "automated access",
        "bot detection",
        "suspicious activity",
        "unusual traffic",
        "rate limited",
        "too many requests",
    ]

    # Add timeout constants - REDUCED for better responsiveness
    NAVIGATION_TIMEOUT = 30000  # 30 seconds in milliseconds (reduced from 60s for faster failures)
    WAIT_FOR_LOAD_TIMEOUT = 20000  # 20 seconds in milliseconds (reduced from 45s for faster failures)
    PAGE_TIMEOUT = 60000  # 60 seconds in milliseconds (reduced from 120s for faster failures)

    async def check_for_blocking(page):
        """Check if the page appears to be showing a blocking message"""
        try:
            # Get page title
            title = await page.title()
            # Get visible text on the page
            body_text = await page.evaluate("() => document.body.innerText")

            # Check title and body text for blocking indicators
            title_lower = title.lower()
            body_lower = body_text.lower()

            for indicator in blocking_indicators:
                if indicator in title_lower or indicator in body_lower:
                    logger.warning(
                        "Blocking content detected on page", {"url": url, "indicator": indicator}
                    )
                    return True

            # Check if page has very little content (might be a simple block page)
            if len(body_text.strip()) < 100 and ("error" in body_lower or "sorry" in body_lower):
                logger.warning(
                    "Suspiciously small page content detected",
                    {"url": url, "content_length": len(body_text)},
                )
                return True

            # Check for JavaScript challenges
            js_challenge_indicators = await page.evaluate(
                """() => {
                const scripts = Array.from(document.querySelectorAll('script'));
                for (const script of scripts) {
                    if (script.textContent && (
                        script.textContent.includes('challenge') ||
                        script.textContent.includes('security') ||
                        script.textContent.includes('captcha') ||
                        script.textContent.includes('cloudflare')
                    )) {
                        return true;
                    }
                }
                return false;
            }"""
            )

            if js_challenge_indicators:
                logger.warning("JavaScript security challenge detected", {"url": url})
                return True

            return False
        except Exception as e:
            logger.error("Error checking for blocking content", {"error": str(e)})
            return False

    async def setup_stealth_browser(playwright, use_proxy=False):
        """Set up a browser with stealth features to avoid detection"""
        # Select a random user agent
        user_agent = random.choice(USER_AGENTS)

        # Browser launch options
        browser_options = {
            "headless": True,
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-features=IsolateOrigins,site-per-process",
                "--disable-web-security",
                "--disable-site-isolation-trials",
            ],
        }

        # Add proxy if requested and properly configured
        if use_proxy:
            # Try to get proxy credentials from environment
            import os
            proxy_username = os.getenv("WEBSHARE_PROXY_USERNAME", "")
            proxy_password = os.getenv("WEBSHARE_PROXY_PASSWORD", "")

            # Validate proxy credentials (allow admin/admin for webshare, block other placeholders)
            valid_credentials = (
                proxy_username and proxy_password and
                proxy_username.strip() and proxy_password.strip() and
                not (proxy_username.lower() in ["test", "placeholder"] or
                     proxy_password.lower() in ["test", "placeholder"])
            )

            if valid_credentials:
                proxy_config = {
                    "server": "p.webshare.io:9999",
                    "username": proxy_username,
                    "password": proxy_password,
                }
                browser_options["proxy"] = proxy_config
                logger.info("Using webshare proxy with valid credentials")
            else:
                logger.warning(
                    "Webshare proxy requested but credentials are invalid or placeholder values. "
                    "Falling back to direct connection.",
                    {"username_provided": bool(proxy_username), "password_provided": bool(proxy_password)}
                )
                # Don't use proxy if credentials are invalid
                use_proxy = False

        # Launch the browser
        browser = await playwright.chromium.launch(**browser_options)

        # Create a new browser context with the stealth settings
        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent=user_agent,
            is_mobile=False,
            has_touch=False,
            locale="en-US",
            timezone_id="America/New_York",
            permissions=["geolocation"],
            bypass_csp=True,
        )

        # Add JavaScript to evade detection
        await context.add_init_script(
            """
            () => {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => false,
                });
                
                window.chrome = {
                    runtime: {},
                };
                
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {
                                type: "application/x-google-chrome-pdf",
                                suffixes: "pdf",
                                description: "Portable Document Format",
                                enabledPlugin: Plugin
                            },
                            description: "Chrome PDF Plugin",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        }
                    ],
                });
                
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            }
            """
        )

        # Create a new page
        page = await context.new_page()

        # Additional page stealth settings
        await page.evaluate(
            """
            () => {
                history.pushState = history.pushState || function () {};
                history.replaceState = history.replaceState || function () {};
                window.getComputedStyle = window.getComputedStyle || function () {};
            }
            """
        )

        return browser, page

    async def try_direct_connection(playwright, url, logger, max_retries=1):
        """Try direct connection to URL"""
        # Record direct connection attempt
        increment_direct_attempt(get_domain_without_prefix(url), endpoint_type, org_id)
        
        for retry in range(max_retries + 1):
            browser = None
            page = None
            try:
                browser, page = await setup_stealth_browser(playwright)
                await page.goto(url, timeout=NAVIGATION_TIMEOUT)
                
                # Wait for network to be idle (no more than 2 connections for at least 500ms)
                await page.wait_for_load_state("networkidle", timeout=WAIT_FOR_LOAD_TIMEOUT)
                
                # Check if page appears blocked
                is_blocked = await check_for_blocking(page)
                if is_blocked:
                    logger.warning(
                        "Page appears to be blocked with direct connection", {"url": url, "retry": retry}
                    )
                    await browser.close()
                    if retry < max_retries:
                        # Use exponential backoff for retries
                        delay = (2 ** retry) * 1.5
                        logger.info(f"Retrying direct connection in {delay:.2f} seconds")
                        await asyncio.sleep(delay)
                        continue
                    return None, None, False
                
                # Take a screenshot
                screenshot_bytes = await page.screenshot(full_page=True)
                
                # Record direct connection success
                increment_direct_success(get_domain_without_prefix(url), endpoint_type, org_id)
                
                logger.info(
                    "Screenshot captured successfully with direct connection",
                    {
                        "url": url,
                        "screenshot_size": len(screenshot_bytes),
                        "direct_success": "true",
                    },
                )
                
                return browser, page, True
                
            except Exception as e:
                logger.error(
                    "Failed to capture screenshot with direct connection",
                    {"error": str(e), "url": url, "retry": retry},
                )
                if browser:
                    await browser.close()
                if retry < max_retries:
                    # Use exponential backoff for retries
                    delay = (2 ** retry) * 1.5
                    logger.info(f"Retrying direct connection in {delay:.2f} seconds")
                    await asyncio.sleep(delay)
                else:
                    return None, None, False

    async def try_proxy_connection(playwright, url, logger):
        """Try to connect via proxy as a fallback"""
        # Increment proxy connection attempt counter
        increment_proxy_attempt(get_domain_without_prefix(url), endpoint_type, org_id)

        logger.info("Attempting proxy connection for screenshot as fallback")

        # Check if webshare is properly configured before attempting
        import os
        proxy_username = os.getenv("WEBSHARE_PROXY_USERNAME", "")
        proxy_password = os.getenv("WEBSHARE_PROXY_PASSWORD", "")

        valid_credentials = (
            proxy_username and proxy_password and
            proxy_username.strip() and proxy_password.strip() and
            not (proxy_username.lower() in ["test", "placeholder"] or
                 proxy_password.lower() in ["test", "placeholder"])
        )

        if not valid_credentials:
            logger.warning(
                "Proxy connection skipped - webshare credentials are invalid or placeholder values",
                {"url": url, "username_provided": bool(proxy_username)}
            )
            return None, None, False

        browser = None
        page = None
        try:
            browser, page = await setup_stealth_browser(playwright, use_proxy=True)
            # Use a slightly longer timeout for proxy connections
            await page.goto(url, timeout=NAVIGATION_TIMEOUT * 1.2)  # Reduced multiplier from 1.5 to 1.2
            
            # Check if the page still shows blocking content with proxy
            is_blocked = await check_for_blocking(page)
            if is_blocked:
                logger.warning("Page appears to be blocked even with proxy connection")
                await browser.close()
                return None, None, False
            
            # Increment proxy connection success counter
            increment_proxy_success(get_domain_without_prefix(url), endpoint_type, org_id)
            
            logger.info(
                "Screenshot prepared with proxy",
                {
                    "url": url,
                    "proxy_success": "true",
                },
            )
            
            return browser, page, True
            
        except Exception as e:
            logger.error(
                "Failed to navigate to URL with proxy for screenshot", {"error": str(e), "url": url}
            )
            if browser:
                await browser.close()
            return None, None, False

    # The main screenshot capture workflow with timeout
    async def _screenshot_workflow():
        import playwright.async_api
        p = await playwright.async_api.async_playwright().start()
        
        try:
            # First try direct connection
            browser, page, success = await try_direct_connection(p, url, logger, max_retries=1)
            
            # If direct connection failed, try proxy
            if not success:
                browser, page, success = await try_proxy_connection(p, url, logger)
                
            if not success:
                logger.error(
                    f"Failed to access {url} for screenshot with both direct and proxy connections"
                )
                return None
            
            # Wait for images to load before taking screenshot
            # Platform-specific image loading wait times (in milliseconds)
            social_media_platforms = {
                "instagram.com": 5000,    # Instagram needs more time for image loading
                "facebook.com": 4000,     # Facebook has complex image loading
                "fb.com": 4000,          # Facebook short domain
                "twitter.com": 3500,     # Twitter/X has dynamic image loading
                "x.com": 3500,           # Twitter rebrand
                "linkedin.com": 4000,    # LinkedIn has professional images
                "youtube.com": 4500,     # YouTube thumbnails and video previews
                "youtu.be": 4500,        # YouTube short domain
                "pinterest.com": 5000,   # Pinterest is image-heavy
                "pin.it": 5000,          # Pinterest short domain
                "tiktok.com": 4000,      # TikTok video thumbnails
            }

            # Check if URL matches any social media platform
            url_lower = url.lower()
            image_load_wait = 3000  # Default 3 seconds for regular websites

            for platform, wait_time in social_media_platforms.items():
                if platform in url_lower:
                    image_load_wait = wait_time
                    break

            logger.info(f"Waiting {image_load_wait}ms for images to load...")
            await page.wait_for_timeout(image_load_wait)

            # Additional wait for network activity to settle (especially for social media)
            if any(platform in url_lower for platform in ["instagram.com", "facebook.com", "twitter.com", "x.com", "linkedin.com", "youtube.com", "pinterest.com"]):
                logger.info("Social media platform detected - waiting for network to settle...")
                try:
                    # Wait for network to be mostly idle (allows some ongoing requests)
                    await page.wait_for_load_state("networkidle", timeout=5000)
                    logger.info("Network settled for social media platform")
                except Exception as e:
                    logger.info(f"Network settle timeout (expected for some platforms): {str(e)}")

            # Take screenshot
            screenshot_bytes = await page.screenshot(full_page=True)
            
            # Clean up
            await browser.close()
            
            # Convert to data URL
            from base64 import b64encode
            data_url = f"data:image/png;base64,{b64encode(screenshot_bytes).decode('utf-8')}"
            return data_url
        except Exception as e:
            logger.error(f"Error in screenshot capture: {str(e)}")
            return None
        finally:
            await p.stop()

    try:
        # Apply global timeout to the entire process
        return await asyncio.wait_for(_screenshot_workflow(), timeout=timeout)
    except asyncio.TimeoutError:
        logger.error(f"Screenshot capture timed out after {timeout} seconds for URL: {url}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in screenshot capture process: {str(e)}")
        return None


def get_screenshot_from_url(url, logger: ConsoleLogger):
    auth_string = f"{admin_username}:{admin_password}"
    encoded_auth = b64encode(auth_string.encode()).decode()
    headers = {"Authorization": f"Basic {encoded_auth}"}

    url = f"{base_url}/api/admin/page/screenshot?url={url}"

    for attempt in range(3):  # Retry up to 3 times
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json().get("screenshot")
        elif attempt < 2:  # Log retry attempt if not the last one
            logger.warning(
                "Failed to get screenshot from URL, retrying",
                {"attempt": attempt + 1, "status_code": response.status_code},
            )
            # Use exponential backoff: 1s, 2s for retries
            time.sleep(attempt + 1)

    raise Exception(
        f"get_screenshot_from_url: Request failed after 3 attempts: {response.status_code} - {response.text}"
    )


def local_image_to_data_url(image_path):
    """Convert a local image to a data URL."""
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = b64encode(image_file.read()).decode()
            ext = Path(image_path).suffix.lstrip(".")
            if ext.lower() == "jpg":
                ext = "jpeg"
            return f"data:image/{ext};base64,{encoded_string}"
    except Exception as e:
        print(f"Error converting image to data URL: {str(e)}")
        return None
