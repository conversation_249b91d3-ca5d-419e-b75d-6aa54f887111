from typing import List, Dict, Any, Optional
import asyncio
import json
from sqlmodel import Session, select
from app.models.db_models import MccAnalysis, WebsiteUrls, ScrapeRequestTracker, get_current_time
from app.models.request_models import MccAnalysisRequest
from app.services.url_processing_service import UrlProcessingService
from app.gpt_models.gemini_model_wrapper.gemini_utils import Gemini<PERSON>lient
from app.gpt_models.gpt_prompts import GptPromptPicker
from app.database import get_session
from app.utils.logger import ConsoleLogger
import pandas as pd
import os

class MccAnalysisService:
    """Service for MCC analysis using the new tool flow"""
    
    def __init__(self):
        self.console_logger = ConsoleLogger("mcc_analysis_service")
        self.url_processor = UrlProcessingService()
        self.gemini_client = GeminiClient("mcc_analysis")
        
    async def process_mcc_analysis(self, request: MccAnalysisRequest) -> Dict[str, Any]:
        """
        Main method to process MCC analysis using the new tool flow
        
        Args:
            request: MCC analysis request containing website and parsed URLs
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            self.console_logger.info(f"Starting MCC analysis for website: {request.website}")
            
            # Create initial database record
            analysis_record = await self._create_analysis_record(request)
            
            # Step 1-3: Extract, dedupe, and sort URLs
            all_urls = self.url_processor.extract_all_urls(request.parsed_urls)
            deduped_urls = self.url_processor.dedupe_urls(all_urls)
            sorted_limited_urls = self.url_processor.sort_and_limit_urls(deduped_urls, limit=100)
            
            # Step 4-5: Find reachable URLs
            reachable_urls = await self.url_processor.find_reachable_urls(sorted_limited_urls, target_count=4)
            
            # Step 6: Get home page URL
            home_page_url = self.url_processor.extract_home_page_url(request.website)
            
            # Step 7: Create final URL list
            final_urls = self.url_processor.create_final_url_list(reachable_urls, home_page_url)
            
            # Step 8: Process each URL individually (max 5 calls)
            analysis_results = await self._analyze_urls_individually(final_urls, request.website)
            
            # Perform MCC classification based on collected data
            mcc_result = await self._classify_mcc(analysis_results, request.website)
            
            # Update database record with results
            await self._update_analysis_record(analysis_record.id, mcc_result, "COMPLETED")
            
            self.console_logger.info(f"MCC analysis completed for website: {request.website}")
            
            return {
                "success": True,
                "analysis_id": analysis_record.id,
                "website": request.website,
                "mcc_code": mcc_result.get("mcc", ""),
                "business_category": mcc_result.get("business_category", ""),
                "business_description": mcc_result.get("business_desc", ""),
                "reasoning": mcc_result.get("reason", ""),
                "processed_urls": final_urls,
                "total_urls_processed": len(final_urls)
            }
            
        except Exception as e:
            self.console_logger.error(f"Error in MCC analysis: {str(e)}")
            if 'analysis_record' in locals():
                await self._update_analysis_record(analysis_record.id, {}, "FAILED", str(e))
            
            return {
                "success": False,
                "error": str(e),
                "website": request.website
            }
    
    async def _create_analysis_record(self, request: MccAnalysisRequest) -> MccAnalysis:
        """Create initial MCC analysis record in database"""
        try:
            session = next(get_session())
            
            analysis_record = MccAnalysis(
                website=request.website,
                scrape_request_ref_id=request.scrapeRequestRefID,
                result_status="PROCESSING",
                started_at=get_current_time(),
                org_id=str(request.org_id)
            )
            
            session.add(analysis_record)
            session.commit()
            session.refresh(analysis_record)
            
            # Also create tracker record
            tracker_record = ScrapeRequestTracker(
                scrape_request_ref_id=request.scrapeRequestRefID,
                analysis_type="mcc",
                website=request.website,
                analysis_id=analysis_record.id,
                status="PROCESSING"
            )
            
            session.add(tracker_record)
            session.commit()
            session.close()
            
            self.console_logger.info(f"Created analysis record with ID: {analysis_record.id}")
            return analysis_record
            
        except Exception as e:
            self.console_logger.error(f"Error creating analysis record: {str(e)}")
            raise
    
    async def _analyze_urls_individually(self, urls: List[str], website: str) -> List[Dict[str, Any]]:
        """
        Step 8: Process URLs individually (max 5 calls)
        
        Args:
            urls: List of URLs to analyze (max 5)
            website: Main website URL
            
        Returns:
            List of analysis results for each URL
        """
        analysis_results = []
        
        for i, url in enumerate(urls[:5]):  # Ensure max 5 calls
            self.console_logger.info(f"Analyzing URL {i+1}/{len(urls)}: {url}")
            
            try:
                # Use the new prompt for website analysis
                prompt = GptPromptPicker.summarize_website(text=f"Please analyze this website: {url}", image=None)
                
                response = self.gemini_client.generate_content(
                    prompt=prompt,
                    task_type="website_analysis",
                    website_url=url,
                    temperature=0,
                    max_output_tokens=4000,
                    enable_url_context=True,
                    enable_google_search=False
                )
                
                if response.get("success", False):
                    # Parse the response
                    response_text = response.get("text", "")
                    
                    # Try to parse JSON response
                    try:
                        parsed_result = json.loads(response_text)
                        analysis_results.append({
                            "url": url,
                            "analysis": parsed_result,
                            "success": True
                        })
                    except json.JSONDecodeError:
                        # If not valid JSON, store as text
                        analysis_results.append({
                            "url": url,
                            "analysis": {"raw_text": response_text},
                            "success": True
                        })
                else:
                    analysis_results.append({
                        "url": url,
                        "analysis": {},
                        "success": False,
                        "error": response.get("error", "Unknown error")
                    })
                    
            except Exception as e:
                self.console_logger.error(f"Error analyzing URL {url}: {str(e)}")
                analysis_results.append({
                    "url": url,
                    "analysis": {},
                    "success": False,
                    "error": str(e)
                })
        
        return analysis_results
    
    async def _classify_mcc(self, analysis_results: List[Dict[str, Any]], website: str) -> Dict[str, Any]:
        """
        Classify MCC based on analysis results
        
        Args:
            analysis_results: Results from individual URL analysis
            website: Main website URL
            
        Returns:
            MCC classification result
        """
        try:
            # Combine all analysis data
            combined_info = {
                "product_services": "",
                "line_of_business": "",
                "customers": "",
                "website_description": ""
            }
            
            # Extract information from analysis results
            for result in analysis_results:
                if result.get("success", False):
                    analysis = result.get("analysis", {})
                    
                    # Combine product/services information
                    if "product_services" in analysis:
                        combined_info["product_services"] += f" {analysis['product_services']}"
                    
                    # Combine line of business information
                    if "line_of_business" in analysis:
                        combined_info["line_of_business"] += f" {analysis['line_of_business']}"
                    
                    # Combine customer information
                    if "customers" in analysis:
                        combined_info["customers"] += f" {analysis['customers']}"
                    
                    # Combine website description
                    if "website_description" in analysis:
                        combined_info["website_description"] += f" {analysis['website_description']}"
            
            # Clean up combined information
            for key in combined_info:
                combined_info[key] = combined_info[key].strip()
            
            # Load MCC data for classification
            mcc_info, excluded_mccs, filtered_special_mcc, filtered_visa_data = self._load_mcc_data()
            
            # Generate MCC classification prompt
            prompt = GptPromptPicker.get_website_mcc_classifier_prompt_full(
                mcc_info=mcc_info,
                excluded_mccs=excluded_mccs,
                website_info=combined_info,
                filtered_special_mcc=filtered_special_mcc,
                filtered_visa_data=filtered_visa_data,
                website_url=website
            )
            
            # Get MCC classification
            response = self.gemini_client.classify_mcc(
                website_url=website,
                website_info=combined_info,
                prompt=prompt
            )
            
            if response.get("success", False):
                response_text = response.get("text", "")
                try:
                    mcc_result = json.loads(response_text)
                    return mcc_result
                except json.JSONDecodeError:
                    return {"mcc": "-1", "business_desc": "Error parsing response", "business_category": "Unknown", "reason": "JSON parsing failed", "website": website}
            else:
                return {"mcc": "-1", "business_desc": "Analysis failed", "business_category": "Unknown", "reason": "API call failed", "website": website}
                
        except Exception as e:
            self.console_logger.error(f"Error in MCC classification: {str(e)}")
            return {"mcc": "-1", "business_desc": "Error in classification", "business_category": "Unknown", "reason": str(e), "website": website}
    
    def _load_mcc_data(self) -> tuple:
        """Load MCC data from CSV files"""
        try:
            # Load special MCCs
            special_mcc_path = "app/input/special_mccs.csv"
            if os.path.exists(special_mcc_path):
                special_mcc_df = pd.read_csv(special_mcc_path)
                filtered_special_mcc = special_mcc_df.to_dict('records')
            else:
                filtered_special_mcc = []
            
            # Load Visa MCC data
            visa_mcc_path = "app/input/combined_mcc_data(1).csv"
            if os.path.exists(visa_mcc_path):
                visa_mcc_df = pd.read_csv(visa_mcc_path)
                filtered_visa_data = visa_mcc_df.to_dict('records')
            else:
                filtered_visa_data = []
            
            return {}, [], filtered_special_mcc, filtered_visa_data
            
        except Exception as e:
            self.console_logger.error(f"Error loading MCC data: {str(e)}")
            return {}, [], [], []
    
    async def _update_analysis_record(self, analysis_id: int, mcc_result: Dict[str, Any], status: str, error_message: str = None):
        """Update analysis record with results"""
        try:
            session = next(get_session())
            
            # Update MCC analysis record
            analysis_record = session.get(MccAnalysis, analysis_id)
            if analysis_record:
                analysis_record.result_status = status
                analysis_record.mcc_code = mcc_result.get("mcc", "")
                analysis_record.business_category = mcc_result.get("business_category", "")
                analysis_record.business_description = mcc_result.get("business_desc", "")
                analysis_record.reasoning = mcc_result.get("reason", "")
                analysis_record.completed_at = get_current_time()
                analysis_record.last_updated = get_current_time()
                
                if error_message:
                    analysis_record.error_message = error_message
                    analysis_record.failed_at = get_current_time()
                
                session.commit()
            
            # Update tracker record
            tracker_record = session.exec(
                select(ScrapeRequestTracker).where(
                    ScrapeRequestTracker.analysis_id == analysis_id
                )
            ).first()
            
            if tracker_record:
                tracker_record.status = status
                tracker_record.completed_at = get_current_time()
                if error_message:
                    tracker_record.error_message = error_message
                session.commit()
            
            session.close()
            
        except Exception as e:
            self.console_logger.error(f"Error updating analysis record: {str(e)}")
            raise
