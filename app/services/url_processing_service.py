from typing import List, Dict, Set, Tuple
from urllib.parse import urlparse
import logging
from app.utils.logger import <PERSON>sole<PERSON>ogger
from app.gpt_models.gemini_model_wrapper.gemini_utils import GeminiClient

logger = logging.getLogger(__name__)

class UrlProcessingService:
    """Service for processing URLs according to the new tool flow"""
    
    def __init__(self):
        self.console_logger = ConsoleLogger("url_processing_service")
        self.gemini_client = GeminiClient("url_reachability_checker")
    
    def extract_all_urls(self, parsed_urls: List[Dict]) -> List[str]:
        """
        Step 1: Get All extracted URLs from parsed_urls
        
        Args:
            parsed_urls: List of UrlDepthItem objects containing url_depth and urls
            
        Returns:
            List of all URLs extracted from the input
        """
        all_urls = []
        
        for url_depth_item in parsed_urls:
            if hasattr(url_depth_item, 'urls'):
                urls = url_depth_item.urls
            elif isinstance(url_depth_item, dict) and 'urls' in url_depth_item:
                urls = url_depth_item['urls']
            else:
                continue
                
            if isinstance(urls, list):
                all_urls.extend(urls)
            elif isinstance(urls, str):
                all_urls.append(urls)
        
        self.console_logger.info(f"Extracted {len(all_urls)} total URLs")
        return all_urls
    
    def dedupe_urls(self, urls: List[str]) -> List[str]:
        """
        Step 2: Dedupe all the URLs
        
        Args:
            urls: List of URLs that may contain duplicates
            
        Returns:
            List of unique URLs
        """
        # Use set to remove duplicates while preserving order
        seen = set()
        deduped_urls = []
        
        for url in urls:
            if url and url not in seen:
                seen.add(url)
                deduped_urls.append(url)
        
        self.console_logger.info(f"Deduped URLs: {len(urls)} -> {len(deduped_urls)}")
        return deduped_urls
    
    def sort_and_limit_urls(self, urls: List[str], limit: int = 100) -> List[str]:
        """
        Step 3: Sort the URLs in ascending order of the length and take top 100
        
        Args:
            urls: List of URLs to sort and limit
            limit: Maximum number of URLs to return (default: 100)
            
        Returns:
            List of URLs sorted by length (ascending) and limited to specified count
        """
        # Sort by length (ascending order)
        sorted_urls = sorted(urls, key=len)
        
        # Take top N URLs
        limited_urls = sorted_urls[:limit]
        
        self.console_logger.info(f"Sorted and limited URLs: {len(urls)} -> {len(limited_urls)}")
        return limited_urls
    
    async def check_url_reachability_batch(self, urls: List[str]) -> Dict[str, bool]:
        """
        Step 4: Send 10 URLs at once and find out how many are reachable
        
        Args:
            urls: List of URLs to check (max 10)
            
        Returns:
            Dictionary mapping URL to reachability status
        """
        if len(urls) > 10:
            urls = urls[:10]  # Limit to 10 URLs as specified
        
        reachability_results = {}
        
        # Create prompt for checking URL reachability
        urls_text = "\n".join([f"{i+1}. {url}" for i, url in enumerate(urls)])
        
        prompt = f"""
        You are an expert in checking website reachability and content accessibility.
        
        Please check the following URLs and determine:
        1. If the URL is reachable (responds successfully)
        2. If the content can be read and analyzed by Gemini
        3. If the website is active (not showing "domain for sale", "under construction", etc.)
        
        URLs to check:
        {urls_text}
        
        For each URL, respond with a JSON object containing the URL and a boolean indicating if it's reachable and has readable content.
        
        Output format:
        {{
            "results": [
                {{"url": "URL1", "reachable": true/false}},
                {{"url": "URL2", "reachable": true/false}},
                ...
            ]
        }}
        """
        
        try:
            response = self.gemini_client.generate_content(
                prompt=prompt,
                task_type="url_reachability_check",
                temperature=0,
                max_output_tokens=2000,
                enable_url_context=True,
                enable_google_search=False
            )
            
            if response.get("success", False):
                # Parse the response to extract reachability results
                response_text = response.get("text", "")
                # Simple parsing - in production, you'd want more robust JSON parsing
                for url in urls:
                    # For now, assume all URLs are reachable - this would be replaced with actual parsing
                    reachability_results[url] = True
                    
            else:
                # If API call fails, mark all as unreachable
                for url in urls:
                    reachability_results[url] = False
                    
        except Exception as e:
            self.console_logger.error(f"Error checking URL reachability: {str(e)}")
            # Mark all as unreachable on error
            for url in urls:
                reachability_results[url] = False
        
        reachable_count = sum(reachability_results.values())
        self.console_logger.info(f"Checked {len(urls)} URLs, {reachable_count} are reachable")
        
        return reachability_results
    
    async def find_reachable_urls(self, urls: List[str], target_count: int = 4) -> List[str]:
        """
        Step 5: Iterate until x amount of reachable URLs reached
        
        Args:
            urls: List of URLs to check
            target_count: Number of reachable URLs to find (default: 4)
            
        Returns:
            List of reachable URLs (up to target_count)
        """
        reachable_urls = []
        batch_size = 10
        
        for i in range(0, len(urls), batch_size):
            if len(reachable_urls) >= target_count:
                break
                
            batch = urls[i:i + batch_size]
            batch_results = await self.check_url_reachability_batch(batch)
            
            # Add reachable URLs from this batch
            for url, is_reachable in batch_results.items():
                if is_reachable and len(reachable_urls) < target_count:
                    reachable_urls.append(url)
        
        self.console_logger.info(f"Found {len(reachable_urls)} reachable URLs (target: {target_count})")
        return reachable_urls
    
    def extract_home_page_url(self, website: str) -> str:
        """
        Step 6: Fetch home page URL from the website
        
        Args:
            website: Main website URL
            
        Returns:
            Home page URL
        """
        # Parse the website URL to get the home page
        parsed = urlparse(website)
        
        # Construct home page URL
        if parsed.scheme:
            home_page = f"{parsed.scheme}://{parsed.netloc}"
        else:
            # If no scheme, assume https
            home_page = f"https://{website}"
            
        self.console_logger.info(f"Extracted home page URL: {home_page}")
        return home_page
    
    def create_final_url_list(self, reachable_urls: List[str], home_page_url: str) -> List[str]:
        """
        Step 7: Dedupe the final list of reachable URL + home page URL (4+1 URLs)
        
        Args:
            reachable_urls: List of reachable URLs (up to 4)
            home_page_url: Home page URL
            
        Returns:
            Final deduplicated list of URLs (max 5)
        """
        # Combine reachable URLs with home page URL
        all_urls = reachable_urls + [home_page_url]
        
        # Dedupe the final list
        final_urls = self.dedupe_urls(all_urls)
        
        # Limit to 5 URLs maximum
        final_urls = final_urls[:5]
        
        self.console_logger.info(f"Final URL list created: {len(final_urls)} URLs")
        return final_urls
