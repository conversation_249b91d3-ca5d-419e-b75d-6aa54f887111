import json
import logging
import os
import traceback
from datetime import datetime
from typing import Any, Dict, Optional, Union
from contextvars import Context<PERSON><PERSON>

from app.database import get_session
from app.models.db_models import MccAnalysis

# Context variable to store request context
request_context: ContextVar[Dict[str, Any]] = ContextVar('request_context', default={})


class ConsoleLogger:
    def __init__(self, analysis_id: Union[int, str], scrape_request_id: Optional[str] = None):
        self.analysis_id = analysis_id
        self.scrape_request_id = scrape_request_id
        
        # Only try to get from database if analysis_id is an integer
        if isinstance(analysis_id, int) and not self.scrape_request_id:
            try:
                with next(get_session()) as session:
                    analysis = session.get(MccAnalysis, analysis_id)
                    if analysis:
                        self.scrape_request_id = analysis.scrape_request_ref_id
            except Exception:
                pass

    def _get_log_context(self, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Get the logging context with request correlation and analysis data"""
        context = {
            'analysis_id': str(self.analysis_id),
            'scrape_request_id': self.scrape_request_id or 'NO_REF',
            'timestamp': datetime.now().isoformat()
        }
        
        # Add request context if available
        try:
            request_ctx = request_context.get()
            if request_ctx:
                context.update(request_ctx)
        except LookupError:
            pass
        
        # Add additional data
        if data:
            context['data'] = data
            
        return context

    def _format_message(
        self, level: str, message: str, data: Optional[Dict[str, Any]] = None
    ) -> str:
        """Format message for console output"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prefix = f"[{timestamp}][{self.analysis_id}][{self.scrape_request_id or 'NO_REF'}]"
        if data:
            try:
                data_str = "\n" + json.dumps(data, indent=2, default=str)
            except:
                data_str = f"\n{str(data)}"
        else:
            data_str = ""
        return f"{prefix} {level}: {message}{data_str}"

    def info(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log info message to console"""
        print(self._format_message("INFO", message, data))

    def error(
        self, message: str, error: Optional[Exception] = None, data: Optional[Dict[str, Any]] = None
    ):
        """Log error message to console"""
        # Prepare error data
        error_data = data or {}
        if error:
            error_data["error"] = str(error)
            error_data["error_type"] = type(error).__name__
        
        # Console logging
        print(self._format_message("ERROR", message, error_data))

        # Then print the traceback in its original format (console only)
        try:
            if error:
                print(f"Traceback for analysis {self.analysis_id}:")
                traceback.print_exception(type(error), error, error.__traceback__)
            else:
                # Skip traceback printing when no exception is provided
                # This prevents issues in the Celery worker environment
                pass
        except Exception as tb_error:
            print(f"Error printing traceback: {str(tb_error)}")

    def debug(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log debug message to console"""
        print(self._format_message("DEBUG", message, data))

    def warning(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log warning message to console"""
        print(self._format_message("WARNING", message, data))

    def critical(self, message: str, data: Optional[Dict[str, Any]] = None):
        """Log critical message to console"""
        print(self._format_message("CRITICAL", message, data))


# FastAPI middleware for request correlation
def setup_request_logging_middleware(app, verbose_logging=True):
    """Setup FastAPI middleware for request correlation logging
    
    Args:
        app: FastAPI application instance
        verbose_logging: If True, logs detailed request information including headers
    """
    from fastapi import Request
    import uuid
    import time
    
    @app.middleware("http")
    async def request_logging_middleware(request: Request, call_next):
        # Start timing
        start_time = time.time()
        
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Set request context
        context = {
            'request_id': request_id,
            'method': request.method,
            'url': str(request.url),
            'client_ip': request.client.host if request.client else None,
            'user_agent': request.headers.get('user-agent')
        }
        request_context.set(context)
        
        # Log request start immediately
        logger = ConsoleLogger("request_middleware")
        
        # Prepare log data
        log_data = {
            'request_id': request_id,
            'client_ip': context['client_ip'],
            'user_agent': context['user_agent'][:100] if context['user_agent'] else None
        }
        
        # Add more details if verbose logging is enabled
        if verbose_logging:
            log_data.update({
                'query_params': dict(request.query_params) if request.query_params else None,
                'headers': dict(request.headers) if request.headers else None,
                'url_full': str(request.url)
            })
        
        logger.info(f"Request started: {request.method} {request.url.path}", log_data)
        
        try:
            response = await call_next(request)
            context['status_code'] = response.status_code
            
            # Calculate response time
            response_time = time.time() - start_time
            context['response_time'] = response_time
            
            # Log request completion
            logger.info(f"Request completed: {request.method} {request.url.path} - {response.status_code}", {
                'request_id': request_id,
                'status_code': response.status_code,
                'response_time_ms': round(response_time * 1000, 2)
            })
            
            return response
        except Exception as e:
            context['error'] = str(e)
            context['error_type'] = type(e).__name__
            
            # Calculate response time
            response_time = time.time() - start_time
            
            # Log request error
            logger.error(f"Request failed: {request.method} {request.url.path}", error=e, data={
                'request_id': request_id,
                'response_time_ms': round(response_time * 1000, 2)
            })
            raise
    
    return request_logging_middleware


# Utility function to get current request context
def get_request_context() -> Dict[str, Any]:
    """Get current request context for logging"""
    try:
        return request_context.get()
    except LookupError:
        return {}


# Utility function to set request context (useful for background tasks)
def set_request_context(context: Dict[str, Any]):
    """Set request context for logging (useful for background tasks)"""
    request_context.set(context)
