from sqlmodel import Session, select
from app.models.db_models import WebsiteUrls, Website
from app.database import engine
from app.models.request_models import MccAnalysisRequest
from typing import Optional, Union, List, Dict, Any
import logging

# Set up logger for this module
logger = logging.getLogger(__name__)


def store_urls_from_request(request: Union[MccAnalysisRequest], 
                           session: Optional[Session] = None,
                           auto_classify: bool = False) -> int:
    """
    Store URLs from request using scrape_request_ref_id as the primary identifier.
    Returns 1 for success, -1 for failure (no longer returns website_id).
    
    Args:
        request: The request containing URLs to store
        session: Optional existing database session
        auto_classify: Whether to trigger URL classification after storing
        
    Returns:
        int: 1 for success, -1 for failure
    """
    
    # Get a database session if not provided
    close_session = False
    if session is None:
        try:
            session = Session(engine)
            close_session = True
        except Exception as session_error:
            logger.error(f"Failed to create database session: {str(session_error)}")
            return -1
    
    try:
        # Extract request parameters with validation
        try:
            website_url = str(request.website) if request.website else None
            if not website_url:
                logger.error("No website URL provided in request")
                return -1
        except Exception as url_error:
            logger.error(f"Error extracting website URL: {str(url_error)}")
            return -1
        
        try:
            org_id = getattr(request, "org_id", None) or "default"
        except Exception:
            org_id = "default"
            
        try:
            scrape_request_ref_id = getattr(request, "scrapeRequestRefID", None)
            if not scrape_request_ref_id:
                logger.error("No scrapeRequestRefID provided in request") 
                return -1
        except Exception:
            logger.error("Error extracting scrapeRequestRefID from request")
            return -1
        
        # Check if URLs already exist for this scrape_request_ref_id
        existing_urls = session.exec(
            select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == scrape_request_ref_id
            )
        ).all()
        
        if existing_urls:
            logger.info(f"URLs already exist for scrape_request_ref_id: {scrape_request_ref_id}, count: {len(existing_urls)}")
            return 1  # Success - URLs already stored
        
        # Store all URLs using scrape_request_ref_id
        try:
            if hasattr(request, "parsed_urls") and request.parsed_urls:
                new_urls_to_insert = []
                urls_processed = 0
                
                for url_depth_item in request.parsed_urls:
                    try:
                        depth = getattr(url_depth_item, 'url_depth', 1)
                        urls_list = getattr(url_depth_item, 'urls', [])
                        
                        if not isinstance(urls_list, list):
                            logger.warning(f"URLs list is not a list for depth {depth}")
                            continue
                        
                        for url in urls_list:
                            try:
                                # Convert HttpUrl to string safely
                                url_str = str(url) if url else None
                                if not url_str:
                                    continue
                                
                                # Create new URL with scrape_request_ref_id as primary reference
                                new_url = WebsiteUrls(
                                    scrape_request_ref_id=scrape_request_ref_id,
                                    website=website_url,
                                    url=url_str,
                                    depth=depth,
                                    org_id=org_id
                                )
                                new_urls_to_insert.append(new_url)
                                urls_processed += 1
                                
                            except Exception as url_processing_error:
                                logger.warning(f"Error processing URL {url}: {str(url_processing_error)}")
                                continue
                                
                    except Exception as depth_processing_error:
                        logger.warning(f"Error processing URL depth item: {str(depth_processing_error)}")
                        continue
                
                # Batch insert all URLs at once
                if new_urls_to_insert:
                    try:
                        session.add_all(new_urls_to_insert)
                        session.commit()
                        logger.info(f"Successfully stored {len(new_urls_to_insert)} URLs for scrape_request_ref_id: {scrape_request_ref_id}")
                    except Exception as batch_insert_error:
                        logger.error(f"Error in batch URL insert: {str(batch_insert_error)}")
                        try:
                            session.rollback()
                        except Exception:
                            pass
                        return -1
                else:
                    logger.warning("No valid URLs to insert")
                    return -1
                    
            else:
                logger.warning("No parsed_urls found in request")
                return -1
                
        except Exception as url_storage_error:
            logger.error(f"Error in URL storage process: {str(url_storage_error)}")
            return -1
        
        # Optionally trigger URL classification
        if auto_classify and scrape_request_ref_id:
            try:
                trigger_url_classification_v2(website_url, scrape_request_ref_id, org_id)
            except Exception as classification_error:
                logger.error(f"Error triggering URL classification: {str(classification_error)}")
                # Don't fail, classification is optional
        
        return 1  # Success
    
    except Exception as general_error:
        logger.error(f"Unexpected error in store_urls_from_request: {str(general_error)}")
        try:
            if session:
                session.rollback()
        except Exception:
            pass
        return -1
    
    finally:
        # Close session if we created it
        if close_session and session:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"Error closing database session: {str(close_error)}")


def get_urls_by_scrape_ref(scrape_request_ref_id: str, session: Optional[Session] = None) -> List[Dict]:
    """
    Get URLs by scrape request reference ID - simplified approach using scrape_request_ref_id directly.
    
    Args:
        scrape_request_ref_id: The scrape request reference ID
        session: Optional existing database session
        
    Returns:
        List[Dict]: List of URL info dictionaries (empty list if failed)
    """
    # Validate input
    if not scrape_request_ref_id:
        logger.error("No scrape_request_ref_id provided")
        return []
    
    # Get a database session if not provided
    close_session = False
    if session is None:
        try:
            session = Session(engine)
            close_session = True
        except Exception as session_error:
            logger.error(f"Failed to create database session: {str(session_error)}")
            return []
    
    try:
        # Directly query URLs by scrape_request_ref_id - much simpler now!
        try:
            urls_statement = select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == scrape_request_ref_id
            )
            url_records = session.exec(urls_statement).all()
            
            if not url_records:
                logger.warning(f"No URLs found for scrape_request_ref_id: {scrape_request_ref_id}")
                return []
            
            # Convert to dictionaries
            urls_data = []
            for url_record in url_records:
                try:
                    url_dict = {
                        'id': url_record.id,
                        'url': url_record.url,
                        'website': url_record.website,
                        'depth': url_record.depth,
                        'soft_class': url_record.soft_class,
                        'priority_url': url_record.priority_url,
                        'extracted_text': url_record.extracted_text,
                        'img_url': url_record.img_url,
                        'policy': url_record.policy,
                        'registered_name': url_record.registered_name,
                        'org_id': url_record.org_id,
                        'scrape_request_ref_id': url_record.scrape_request_ref_id
                    }
                    urls_data.append(url_dict)
                except Exception as record_error:
                    logger.warning(f"Error processing URL record {url_record.id}: {str(record_error)}")
                    continue
            
            logger.info(f"Retrieved {len(urls_data)} URLs for scrape_request_ref_id: {scrape_request_ref_id}")
            return urls_data
            
        except Exception as query_error:
            logger.error(f"Error querying URLs: {str(query_error)}")
            return []
        # Remaining code was here - already handled above
    
    except Exception as general_error:
        logger.error(f"Unexpected error in get_urls_by_scrape_ref: {str(general_error)}")
        return []
    
    finally:
        # Close session if we created it
        if close_session and session:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"Error closing database session: {str(close_error)}")


def trigger_url_classification_v2(website: str, scrape_request_ref_id: str, org_id: str) -> None:
    """
    Trigger URL classification using scrape_request_ref_id (no website_id dependency)
    
    Args:
        website: The website URL
        scrape_request_ref_id: The scrape request reference ID
        org_id: The organization ID
    """
    try:
        # Validate inputs
        if not website:
            logger.error("No website URL provided for classification")
            return
            
        if not scrape_request_ref_id:
            logger.error("No scrape_request_ref_id provided for classification")
            return
            
        if not org_id:
            org_id = "default"
        
        # COMMENTED OUT: URL classification not available in MCC-only setup
        # Import here to avoid circular imports
        # from app.tasks.celery_tasks import process_url_classification_v2

        # Trigger the Celery task for URL classification (no website_id needed)
        try:
            # task = process_url_classification_v2.delay(
            #     website=website,
            #     scrape_request_ref_id=scrape_request_ref_id,
            #     org_id=org_id
            # )
            # logger.info(f"URL classification v2 task triggered: {task.id}")
            logger.warning("URL classification task not available in MCC-only setup")

        except Exception as task_error:
            logger.error(f"Error triggering URL classification task: {str(task_error)}")
            # Don't re-raise, this is optional functionality
            
    except Exception as trigger_error:
        logger.error(f"Error in trigger_url_classification_v2: {str(trigger_error)}")
        # Don't re-raise, this is optional functionality


def get_classified_urls_by_category(scrape_request_ref_id: str, category: str,
                                   session: Optional[Session] = None) -> List[str]:
    """
    Get classified URLs by category with enhanced error handling
    
    Args:
        scrape_request_ref_id: The scrape request reference ID
        category: The URL category to filter by
        session: Optional existing database session
        
    Returns:
        List[str]: List of URLs in the specified category (empty list if failed)
    """
    # Validate inputs
    if not scrape_request_ref_id:
        logger.error("No scrape_request_ref_id provided")
        return []
        
    if not category:
        logger.error("No category provided")
        return []
    
    # Get a database session if not provided
    close_session = False
    if session is None:
        try:
            session = Session(engine)
            close_session = True
        except Exception as session_error:
            logger.error(f"Failed to create database session: {str(session_error)}")
            return []
    
    try:
        # Import here to avoid circular imports  
        from app.models.db_models import URLClassification
        
        # Query for classified URLs in the specified category
        try:
            classification_statement = select(URLClassification).where(
                URLClassification.scrape_request_ref_id == scrape_request_ref_id,
                URLClassification.classification == category
            )
            classification_results = session.exec(classification_statement).all()
            
            # Extract URLs from results
            urls = []
            for result in classification_results:
                try:
                    url = getattr(result, 'url', None)
                    if url:
                        urls.append(str(url))
                except Exception as url_extract_error:
                    logger.warning(f"Error extracting URL from classification result: {str(url_extract_error)}")
                    continue
            
            logger.info(f"Found {len(urls)} URLs in category '{category}' for ref_id: {scrape_request_ref_id}")
            return urls
            
        except Exception as query_error:
            logger.error(f"Error querying classified URLs: {str(query_error)}")
            return []
    
    except Exception as general_error:
        logger.error(f"Unexpected error in get_classified_urls_by_category: {str(general_error)}")
        return []
    
    finally:
        # Close session if we created it
        if close_session and session:
            try:
                session.close()
            except Exception as close_error:
                logger.error(f"Error closing database session: {str(close_error)}") 