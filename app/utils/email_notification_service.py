"""
Email notification service for error reporting and alerts
"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from typing import Optional, Dict, Any
import traceback
import logging

from app.config import settings
from app.models.db_models import GeneralLogs, get_current_time, Session, engine
from app.utils.logger import ConsoleLogger


class EmailNotificationService:
    """
    Service for sending email notifications about errors and system events
    """
    
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.notification_email = settings.ERROR_NOTIFICATION_EMAIL
        self.from_name = settings.EMAIL_FROM_NAME
        self.logger = ConsoleLogger("EmailNotificationService")
        
    def _validate_email_config(self) -> bool:
        """
        Validate email configuration
        
        Returns:
            bool: True if configuration is valid, False otherwise
        """
        if not self.smtp_username or not self.smtp_password:
            self.logger.error("Email configuration incomplete: SMTP credentials missing")
            return False
            
        if not self.notification_email:
            self.logger.error("Email configuration incomplete: notification email missing")
            return False
            
        return True
    
    def _create_error_email_content(
        self, 
        org_id: str, 
        tool_name: str, 
        error_message: str,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> tuple[str, str]:
        """
        Create email subject and body for error notification
        
        Args:
            org_id (str): Organization ID
            tool_name (str): Name of the tool/analysis that failed
            error_message (str): Error message
            additional_context (Dict, optional): Additional context information
            
        Returns:
            tuple: (subject, body) of the email
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        
        subject = f"🚨 Error Alert: {tool_name} Failed for Org {org_id}"
        
        # Create HTML email body
        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
                .content {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px; }}
                .error-box {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 3px; }}
                .footer {{ color: #6c757d; font-size: 12px; margin-top: 20px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚨 System Error Alert</h2>
                <p>An error occurred in the WebReview DS API system</p>
            </div>
            
            <div class="content">
                <h3>Error Details</h3>
                <table>
                    <tr><th>Timestamp</th><td>{timestamp}</td></tr>
                    <tr><th>Organization ID</th><td>{org_id}</td></tr>
                    <tr><th>Tool/Analysis</th><td>{tool_name}</td></tr>
                    <tr><th>Error Message</th><td class="error-box">{error_message}</td></tr>
                </table>
            </div>
        """
        
        # Add additional context if provided
        if additional_context:
            html_body += """
            <div class="content">
                <h3>Additional Context</h3>
                <table>
            """
            for key, value in additional_context.items():
                html_body += f"<tr><th>{key}</th><td>{value}</td></tr>"
            html_body += "</table></div>"
        
        html_body += f"""
            <div class="footer">
                <p>This is an automated notification from {self.from_name}</p>
                <p>Please investigate and take appropriate action.</p>
            </div>
        </body>
        </html>
        """
        
        return subject, html_body
    
    def send_error_notification(
        self, 
        org_id: str, 
        tool_name: str, 
        error_message: str,
        additional_context: Optional[Dict[str, Any]] = None,
        log_to_db: bool = True
    ) -> bool:
        """
        Send error notification email and optionally log to database
        
        Args:
            org_id (str): Organization ID
            tool_name (str): Name of the tool/analysis that failed
            error_message (str): Error message
            additional_context (Dict, optional): Additional context information
            log_to_db (bool): Whether to log the error to database
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Validate configuration
            if not self._validate_email_config():
                return False
            
            self.logger.info(
                "Preparing error notification email",
                {
                    "org_id": org_id,
                    "tool_name": tool_name,
                    "notification_email": self.notification_email,
                    "log_to_db": log_to_db
                }
            )
            
            # Create email content
            subject, html_body = self._create_error_email_content(
                org_id, tool_name, error_message, additional_context
            )
            
            # Create email message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.smtp_username}>"
            message["To"] = self.notification_email
            
            # Add HTML content
            html_part = MIMEText(html_body, "html")
            message.attach(html_part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_username, self.smtp_password)
                server.sendmail(self.smtp_username, self.notification_email, message.as_string())
            
            self.logger.info(
                "Error notification email sent successfully",
                {
                    "org_id": org_id,
                    "tool_name": tool_name,
                    "recipient": self.notification_email
                }
            )
            
            # Log to database if requested
            if log_to_db:
                self._log_error_to_database(org_id, tool_name, error_message, additional_context)
            
            return True
            
        except Exception as e:
            self.logger.error(
                "Failed to send error notification email",
                {
                    "org_id": org_id,
                    "tool_name": tool_name,
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
            )
            return False
    
    def _log_error_to_database(
        self, 
        org_id: str, 
        tool_name: str, 
        error_message: str,
        additional_context: Optional[Dict[str, Any]] = None
    ):
        """
        Log error information to the GeneralLogs table
        
        Args:
            org_id (str): Organization ID
            tool_name (str): Name of the tool/analysis that failed
            error_message (str): Error message
            additional_context (Dict, optional): Additional context information
        """
        try:
            with Session(engine) as session:
                # Prepare log message
                log_message = f"Error in {tool_name}: {error_message}"
                if additional_context:
                    log_message += f" | Context: {additional_context}"
                
                # Create log entry
                log_entry = GeneralLogs(
                    analysis_id=0,  # Special value for error notifications
                    timestamp=get_current_time(),
                    type=f"error_notification_{tool_name}",
                    messages=log_message,
                    response="Email notification sent",
                    org_id=org_id
                )
                
                session.add(log_entry)
                session.commit()
                
                self.logger.info(
                    "Error logged to database",
                    {
                        "org_id": org_id,
                        "tool_name": tool_name,
                        "log_id": log_entry.id
                    }
                )
                
        except Exception as e:
            self.logger.error(
                "Failed to log error to database",
                {
                    "org_id": org_id,
                    "tool_name": tool_name,
                    "error": str(e)
                }
            )


# Global instance for easy access
email_service = EmailNotificationService()


def send_error_notification(
    org_id: str,
    tool_name: str,
    error_message: str,
    additional_context: Optional[Dict[str, Any]] = None,
    log_to_db: bool = True
) -> bool:
    """
    Convenience function to send error notification

    Args:
        org_id (str): Organization ID
        tool_name (str): Name of the tool/analysis that failed
        error_message (str): Error message
        additional_context (Dict, optional): Additional context information
        log_to_db (bool): Whether to log the error to database

    Returns:
        bool: True if email sent successfully, False otherwise
    """
    return email_service.send_error_notification(
        org_id, tool_name, error_message, additional_context, log_to_db
    )


def notify_analysis_error(
    org_id: str,
    analysis_type: str,
    analysis_id: int,
    website: str,
    scrape_request_ref_id: str,
    error_message: str,
    processing_time: Optional[float] = None
) -> bool:
    """
    Specialized function for notifying analysis errors with standard context

    This function is designed to be easily integrated into existing Celery tasks
    for MCC, Policy, and Risky analysis workflows.

    Args:
        org_id (str): Organization ID
        analysis_type (str): Type of analysis (mcc_analysis, policy_analysis, risky_analysis)
        analysis_id (int): Database ID of the analysis record
        website (str): Website URL being analyzed
        scrape_request_ref_id (str): Scrape request reference ID
        error_message (str): Error message from the analysis
        processing_time (float, optional): Time spent processing before error

    Returns:
        bool: True if notification sent successfully, False otherwise
    """
    try:
        # Prepare additional context with analysis details
        additional_context = {
            "analysis_id": analysis_id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id,
            "analysis_type": analysis_type,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        }

        if processing_time is not None:
            additional_context["processing_time_seconds"] = round(processing_time, 2)

        # Send notification
        return send_error_notification(
            org_id=org_id,
            tool_name=analysis_type,
            error_message=error_message,
            additional_context=additional_context,
            log_to_db=True
        )

    except Exception as e:
        # Log error but don't raise to avoid breaking the main analysis flow
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send analysis error notification: {str(e)}")
        return False


def notify_system_issue(
    issue_type: str,
    issue_message: str,
    component: str,
    severity: str = "HIGH",
    additional_context: Optional[Dict[str, Any]] = None,
    org_id: str = "system"
) -> bool:
    """
    Send system-wide issue notifications for monitoring system health

    This function sends notifications for various system issues like:
    - Database connection failures
    - API service failures (Gemini, OpenAI)
    - Network connectivity issues
    - Celery worker problems
    - Resource exhaustion
    - Configuration errors

    Args:
        issue_type (str): Type of system issue (database, api, network, celery, etc.)
        issue_message (str): Detailed error message
        component (str): System component affected
        severity (str): Issue severity (LOW, MEDIUM, HIGH, CRITICAL)
        additional_context (Dict, optional): Additional context information
        org_id (str): Organization ID (default: "system" for system-wide issues)

    Returns:
        bool: True if notification sent successfully, False otherwise
    """
    try:
        # Create system issue message
        system_message = f"System Issue Detected in {component}: {issue_message}"

        # Prepare system monitoring context
        system_context = {
            "issue_type": issue_type,
            "component": component,
            "severity": severity,
            "notification_type": "system_monitoring",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC"),
            "system_health_alert": True
        }

        # Add additional context if provided
        if additional_context:
            system_context.update(additional_context)

        # Send notification with system monitoring tool name
        return send_error_notification(
            org_id=org_id,
            tool_name=f"system_monitoring_{issue_type}",
            error_message=system_message,
            additional_context=system_context,
            log_to_db=True
        )

    except Exception as e:
        # Log error but don't raise to avoid breaking the main system flow
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send system issue notification: {str(e)}")
        return False


def notify_insufficient_data(
    org_id: str,
    analysis_type: str,
    analysis_id: int,
    website: str,
    scrape_request_ref_id: str,
    mcc_result: int,
    reason: str,
    analysis_details: Optional[Dict[str, Any]] = None,
    processing_time: Optional[float] = None
) -> bool:
    """
    Specialized function for notifying when analysis completes but returns insufficient data (MCC -1)

    This function sends notifications when analysis completes successfully but cannot
    determine a valid MCC code due to insufficient website data.

    Args:
        org_id (str): Organization ID
        analysis_type (str): Type of analysis (mcc_analysis, policy_analysis, etc.)
        analysis_id (int): Database ID of the analysis record
        website (str): Website URL being analyzed
        scrape_request_ref_id (str): Scrape request reference ID
        mcc_result (int): MCC result (typically -1 for insufficient data)
        reason (str): Reason for insufficient data
        analysis_details (Dict, optional): Additional analysis details
        processing_time (float, optional): Time spent processing

    Returns:
        bool: True if notification sent successfully, False otherwise
    """
    try:
        # Create detailed message for insufficient data
        message = f"Analysis completed but returned MCC {mcc_result}: {reason}"

        # Prepare additional context with analysis details
        additional_context = {
            "analysis_id": analysis_id,
            "website": website,
            "scrape_request_ref_id": scrape_request_ref_id,
            "analysis_type": analysis_type,
            "mcc_result": mcc_result,
            "insufficient_data_reason": reason,
            "notification_type": "insufficient_data",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
        }

        if processing_time is not None:
            additional_context["processing_time_seconds"] = round(processing_time, 2)

        # Add analysis details if provided
        if analysis_details:
            additional_context.update(analysis_details)

        # Send notification with specialized tool name
        return send_error_notification(
            org_id=org_id,
            tool_name=f"{analysis_type}_insufficient_data",
            error_message=message,
            additional_context=additional_context,
            log_to_db=True
        )

    except Exception as e:
        # Log error but don't raise to avoid breaking the main analysis flow
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send insufficient data notification: {str(e)}")
        return False
