nohup: ignoring input
2025-07-29 20:57:42,785 - app.main - INFO - Request logging middleware initialized and ready to capture requests
INFO:     Started server process [50341]
INFO:     Waiting for application startup.
2025-07-29 20:57:42,791 - app.main - INFO - Initializing application
2025-07-29 20:57:42,791 - databases - INFO - Connected to database sqlite+aiosqlite:///./sql_app.db
2025-07-29 20:57:42,791 - app.main - INFO - Async database connected successfully
2025-07-29 20:57:42,792 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:42,792 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:42,792 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 20:57:42,792 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_analysis_gemini")
2025-07-29 20:57:42,792 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,792 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,792 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("website_urls_gemini")
2025-07-29 20:57:42,792 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("website_urls_gemini")
2025-07-29 20:57:42,792 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,792 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("general_logs_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("general_logs_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("gemini_api_log_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scrape_request_tracker_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("scraped_urls_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("websites_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("websites_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("mcc_url_classification_gemini")
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine [raw sql] ()
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-29 20:57:42,793 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:57:42,793 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:57:42,794 - app.main - INFO - Database tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
2025-07-29 20:57:51,537 - app.routers.mcc_analysis - INFO - Processing MCC analysis request for https://drynotch.com with ref_id 208378df-sdfgcfeddcdsdc
[2025-07-29 20:57:51][request_middleware][NO_REF] INFO: Request started: POST /mcc-analysis/
{
  "request_id": "d7203a18-1c17-4639-b16e-264dc15d230a",
  "client_ip": "127.0.0.1",
  "user_agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
  "query_params": null,
  "headers": {
    "host": "127.0.0.1:8000",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0",
    "accept": "application/json",
    "accept-language": "en-US,en;q=0.5",
    "accept-encoding": "gzip, deflate, br, zstd",
    "referer": "http://127.0.0.1:8000/docs",
    "content-type": "application/json",
    "content-length": "3174",
    "origin": "http://127.0.0.1:8000",
    "connection": "keep-alive",
    "cookie": "username-127-0-0-1-8888=\"2|1:0|10:1753687981|23:username-127-0-0-1-8888|44:OTI1NWYwNDA2MmYzNGM4ODhmZWEyMzU0NDNhZWYzZGU=|af37f3c5748e269f0b3a691141a8d9d34952092469c0df3eaa2fabbf0681f48d\"; _xsrf=2|a122885f|aa0d74d2af0b3aa32b5dde71a04b1553|1753687981",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "priority": "u=0"
  },
  "url_full": "http://127.0.0.1:8000/mcc-analysis/"
}
2025-07-29 20:57:51,539 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:51,539 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:51,548 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:51,548 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:51,548 INFO sqlalchemy.engine.Engine [generated in 0.00024s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:51,548 - sqlalchemy.engine.Engine - INFO - [generated in 0.00024s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:51,549 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:51,549 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:51,551 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:51,551 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:51,551 INFO sqlalchemy.engine.Engine [generated in 0.00022s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:51,551 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:51,557 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,557 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,557 INFO sqlalchemy.engine.Engine [generated in 0.00032s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,557 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s (insertmanyvalues) 1/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/about-us', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/top-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/size-chart', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/privacy-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/co-ord-sets', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/contact', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,558 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,558 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 14/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/yoga-lite-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 15/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/best-sellers', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 16/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/account/login', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 17/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 18/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/mauve-mist-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 19/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Co-ords', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 20/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/frontpage', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 21/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,559 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,559 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 22/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/navy-nirvana-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 23/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/neon-attack-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 24/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 25/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 26/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/terms-of-service', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 27/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/account/register', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 28/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/sleek-monochrome-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 29/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 30/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/faqs', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,560 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,560 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 31/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 32/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-neon-attack-leggings', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 33/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-yoga-lite-leggings-black', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 34/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/flex-on-quarter-zip-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 35/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/cdn', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 36/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/search?type=product&options%5Bunavailable_products%5D=last&options%5Bprefix%5D=last&&q=Sports+Bra', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 37/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/crop-tops', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 38/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-dare-to-flare-leggings#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 39/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/fiery-trio-co-ord-set#judgeme_product_reviews', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 40/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/citrus-blaze-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 41/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/notchflex-high-waist-ribbed-tights', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,561 INFO sqlalchemy.engine.Engine [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,561 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 42/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/bottom-wear', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 43/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/products/blue-crush-co-ord-set', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 44/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/wishlist', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 45/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/collections/all', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 46/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/policies/refund-policy', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 47/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/wpm@a7f653a7w82d20f99pe720974fm204fcef1/custom/web-pixel-shopify-custom-pixel@0420/sandbox/modern', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - INSERT INTO website_urls_gemini (scrape_request_ref_id, website, url, depth, soft_class, hard_class, priority_url, extracted_text, img_url, policy, registered_name, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 20:57:51,562 INFO sqlalchemy.engine.Engine [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,562 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 48/48 (ordered; batch not supported)] ('208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 'https://drynotch.com/pages/shipping', 1, '', '', 0, '', '', '', '', 'default')
2025-07-29 20:57:51,563 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:57:51,563 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:57:51,565 - app.utils.website_url_processor - INFO - Successfully stored 48 URLs for scrape_request_ref_id: 208378df-sdfgcfeddcdsdc
2025-07-29 20:57:51,567 INFO sqlalchemy.engine.Engine INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 20:57:51,567 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_analysis_gemini (website, scrape_request_ref_id, result_status, mcc_code, business_category, business_description, reasoning, created_at, started_at, completed_at, failed_at, last_updated, error_message, details, org_id, processing_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
2025-07-29 20:57:51,567 INFO sqlalchemy.engine.Engine [generated in 0.00032s] ('https://drynotch.com', '208378df-sdfgcfeddcdsdc', None, None, None, None, None, '2025-07-29T20:57:51.566002Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 20:57:51,567 - sqlalchemy.engine.Engine - INFO - [generated in 0.00032s] ('https://drynotch.com', '208378df-sdfgcfeddcdsdc', None, None, None, None, None, '2025-07-29T20:57:51.566002Z', None, None, None, None, None, None, 'default', 'PENDING')
2025-07-29 20:57:51,568 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:57:51,568 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:57:51,571 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:51,571 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:51,572 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:51,572 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:51,572 INFO sqlalchemy.engine.Engine [generated in 0.00015s] (34,)
2025-07-29 20:57:51,572 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] (34,)
2025-07-29 20:57:51,573 - app.routers.mcc_analysis - INFO - Created new MCC analysis with ID 34
2025-07-29 20:57:51,573 - app.routers.mcc_analysis - INFO - === STARTING ASYNC PROCESSING for analysis 34 ===
2025-07-29 20:57:51,573 - app.routers.mcc_analysis - INFO - Async processing started for analysis 34
2025-07-29 20:57:51,573 - app.routers.mcc_analysis - INFO - Task will process: website=https://drynotch.com, scrape_ref=208378df-sdfgcfeddcdsdc
2025-07-29 20:57:51,573 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 20:57:51,573 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-29 20:57:53,134 - app.routers.mcc_analysis - INFO - === BACKGROUND TASK STARTED for MCC analysis ID: 34 ===
2025-07-29 20:57:53,135 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:53,135 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:53,137 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:53,137 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:53,137 INFO sqlalchemy.engine.Engine [generated in 0.00020s] (34,)
2025-07-29 20:57:53,137 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] (34,)
[2025-07-29 20:57:53][request_middleware][NO_REF] INFO: Request completed: POST /mcc-analysis/ - 200
{
  "request_id": "d7203a18-1c17-4639-b16e-264dc15d230a",
  "status_code": 200,
  "response_time_ms": 1602.17
}
INFO:     127.0.0.1:55616 - "POST /mcc-analysis/ HTTP/1.1" 200 OK
2025-07-29 20:57:53,140 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:53,140 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET started_at=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 20:57:53,141 INFO sqlalchemy.engine.Engine [generated in 0.00029s] ('2025-07-29T20:57:53.139315Z', 'PROCESSING', 34)
2025-07-29 20:57:53,141 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] ('2025-07-29T20:57:53.139315Z', 'PROCESSING', 34)
2025-07-29 20:57:53,142 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:57:53,142 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 20:57:53,154 - app.routers.mcc_analysis - INFO - Updated MccAnalysis 34 status to PROCESSING
2025-07-29 20:57:53,154 - app.routers.mcc_analysis - INFO - Starting MCC classification service for analysis 34
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting MCC analysis process
{
  "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc",
  "org_id": "default"
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 1: Getting URLs data
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Checking if URLs exist in database
{
  "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc"
}
2025-07-29 20:57:53,156 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:53,156 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:53,158 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,158 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,158 INFO sqlalchemy.engine.Engine [generated in 0.00020s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:53,158 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:53,160 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,160 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,160 INFO sqlalchemy.engine.Engine [cached since 1.612s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:53,160 - sqlalchemy.engine.Engine - INFO - [cached since 1.612s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:53,160 INFO sqlalchemy.engine.Engine ROLLBACK
2025-07-29 20:57:53,160 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: URLs found in database
{
  "website": "https://drynotch.com",
  "total_depths": 1,
  "total_urls": 48
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully unpacked URL check result
{
  "urls_exist": true,
  "urls_data_type": "<class 'dict'>"
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Total parsed URLs found: 48
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 2: Preparing data for analysis
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Preparing data for MCC analysis
{
  "website": "https://drynotch.com"
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Got URLs by depth
{
  "depth_1_count": 48,
  "depth_2_count": 0,
  "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc"
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Checking for existing classification results in database
2025-07-29 20:57:53,161 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:57:53,161 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:57:53,161 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,161 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:57:53,161 INFO sqlalchemy.engine.Engine [cached since 0.003789s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:53,161 - sqlalchemy.engine.Engine - INFO - [cached since 0.003789s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:57:56,636 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Found 48 stored URL records
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Stored classification analysis
{
  "total_stored_urls": 48,
  "unreachable_count": 48,
  "classified_count": 0,
  "stored_categories": {
    "urls_not_reachable": 48
  }
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] WARNING: [RACE CONDITION FIX] All stored URLs marked unreachable - forcing fresh classification
{
  "total_urls": 48,
  "unreachable_urls": 48,
  "classified_urls": 0,
  "action": "FORCING_FRESH_CLASSIFICATION",
  "scrape_ref": "208378df-sdfgcfeddcdsdc",
  "fix_type": "RACE_CONDITION_PROTECTION",
  "time": "2025-07-29T20:57:53.162862"
}
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: No usable stored classification results found, proceeding with fresh API calls
[2025-07-29 20:57:53][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting soft classification of URLs
[2025-07-29 20:57:53][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting soft classification
{
  "website": "https://drynotch.com",
  "urls_depth_1_count": 48,
  "urls_depth_2_count": 0
}
[2025-07-29 20:57:53][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Dictionary for soft classification prepared
{
  "url_count": 48,
  "sample_urls": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: Starting URL processing for model policy result
{
  "website": "https://drynotch.com",
  "total_urls": 48
}
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: Token calculation: system=265, base_user=988, available_for_urls=87747
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: Total URL tokens: 817, Available: 87747
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: All URLs fit within token limit
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: Final URLs after token limiting
{
  "original_url_count": 48,
  "final_url_count": 48,
  "urls_trimmed": 0,
  "system_tokens": 265,
  "base_user_tokens": 988,
  "url_tokens": 817,
  "final_total_tokens": 2070,
  "token_limit": 90000,
  "remaining_tokens": 87930
}
[2025-07-29 20:57:53][test-analysis][NO_REF] INFO: Final prompt verification
{
  "actual_prompt_tokens": 2308,
  "token_limit": 90000,
  "within_limit": true,
  "processing_time": 0.15486431121826172
}
[2025-07-29 20:57:53][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Sending request to OpenAI API using model gpt-4o...
[2025-07-29 20:57:53][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Processing URLs for classification. This may take some time...
[2025-07-29 20:57:56][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Usage: CompletionUsage(completion_tokens=134, prompt_tokens=2318, total_tokens=2452, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0))
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Soft classification response received
{
  "response_length": 444,
  "response_preview": "```json\n{\n    \"home_page\": [0],\n    \"about_us\": [3],\n    \"terms_and_condition\": [25],\n    \"returns_cancellation_exchange\": [45],\n    \"privacy_policy\": [7],\n    \"shipping_delivery\": [47],\n    \"contact_..."
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Preparing data for model policy result
{
  "website": "https://drynotch.com",
  "dictionary_1_count": 48,
  "total_chars": 2784,
  "urls_sample": [
    "https://drynotch.com",
    "http://drynotch.com/cdn/shop/files/Untitled-2_dbc3a50a-6538-408a-b16d-aec289edf49d.png?v=1714240398",
    "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush"
  ]
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Got policy URLs classification
{
  "policy_urls": {
    "home_page": [
      0
    ],
    "about_us": [
      3
    ],
    "terms_and_condition": [
      25
    ],
    "returns_cancellation_exchange": [
      45
    ],
    "privacy_policy": [
      7
    ],
    "shipping_delivery": [
      47
    ],
    "contact_us": [
      10
    ],
    "products": [
      2,
      11,
      12,
      16,
      27
    ],
    "services": [],
    "catalogue": [
      8
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Completed soft classification
{
  "mcc_dict": {
    "home_page": [
      "https://drynotch.com"
    ],
    "about_us": [
      "https://drynotch.com/pages/about-us"
    ],
    "terms_and_condition": [
      "https://drynotch.com/policies/terms-of-service"
    ],
    "returns_cancellation_exchange": [
      "https://drynotch.com/policies/refund-policy"
    ],
    "privacy_policy": [
      "https://drynotch.com/policies/privacy-policy"
    ],
    "shipping_delivery": [
      "https://drynotch.com/pages/shipping"
    ],
    "contact_us": [
      "https://drynotch.com/pages/contact"
    ],
    "products": [
      "https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush",
      "https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews",
      "https://drynotch.com/products/work-it-out-straight-fit-pants",
      "https://drynotch.com/products/fiery-trio-co-ord-set",
      "https://drynotch.com/products/sleek-monochrome-set"
    ],
    "services": [],
    "catalogue": [
      "https://drynotch.com/collections"
    ],
    "instagram_page": [],
    "facebook_page": [],
    "twitter_page": [],
    "linkedin_page": [],
    "youtube_page": [],
    "pinterest_page": []
  },
  "total_classified_urls": 13,
  "priority_urls_count": 8
}
[2025-07-29 20:57:57][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully unpacked soft classification result
{
  "output_df_type": "<class 'pandas.core.frame.DataFrame'>",
  "soft_classified_urls_type": "<class 'dict'>"
}
[2025-07-29 20:57:57][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Soft classification unreachable URL analysis
{
  "total_urls": 48,
  "unreachable_count": 0,
  "unreachable_ratio": "0/48",
  "unreachable_percentage": "0.0%",
  "urls_by_category": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 1,
    "returns_cancellation_exchange": 1,
    "privacy_policy": 1,
    "shipping_delivery": 1,
    "contact_us": 1,
    "products": 5,
    "services": 0,
    "catalogue": 1,
    "instagram_page": 0,
    "facebook_page": 0,
    "twitter_page": 0,
    "linkedin_page": 0,
    "youtube_page": 0,
    "pinterest_page": 0
  }
}
[2025-07-29 20:57:57][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Soft classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "products",
    "services",
    "catalogue",
    "instagram_page",
    "facebook_page",
    "twitter_page",
    "linkedin_page",
    "youtube_page",
    "pinterest_page"
  ]
}
[2025-07-29 20:57:57][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting hard classification of URLs
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting hard classification verification for POLICY URLs only
{
  "website": "https://drynotch.com"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'home_page' in hard classification
{
  "url_count": 1
}2025-07-29 20:58:01,679 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 20:58:18,873 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 20:58:18,875 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'about_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'terms_and_condition' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'returns_cancellation_exchange' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'privacy_policy' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'shipping_delivery' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'contact_us' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including other category 'products' in hard classification
{
  "url_count": 5
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including other category 'services' in hard classification
{
  "url_count": 0
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Including policy category 'catalogue' in hard classification
{
  "url_count": 1
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'instagram_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'facebook_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'twitter_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'linkedin_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'youtube_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Skipping social media category 'pinterest_page' from hard classification
{
  "url_count": 0,
  "reason": "social_media_uses_soft_classification_only"
}
[2025-07-29 20:57:57][test-analysis][NO_REF] INFO: Hard classification input
{
  "total_urls": 13,
  "website": "https://drynotch.com",
  "url_limit": 20
}
[2025-07-29 20:57:57][test-analysis][NO_REF] INFO: All URLs fit within hard classification limit
{
  "total_urls": 13,
  "limit": 20
}
[2025-07-29 20:57:57][gemini_optimizer_post_soft_classification][NO_REF] INFO: Optimizing Gemini call for task_type: post_soft_classification
{
  "received_task_type": "post_soft_classification"
}
[2025-07-29 20:58:01][post_soft_classification_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5870,
  "context": {
    "task_type": "post_soft_classification"
  }
}
[2025-07-29 20:58:01][post_soft_classification_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 20:58:18][post_soft_classification_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=461 candidates_tokens_details=None prompt_token_count=1457 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1457
)] thoughts_token_count=1263 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3181 traffic_type=None
[2025-07-29 20:58:18][post_soft_classification_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 347,
  "finish_reason": "STOP"
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Gemini response received for hard classification
{
  "response_length": 347,
  "response_preview": "```json\n{\n    \"home_page\": [5],\n    \"about_us\": [12],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [1, 2, 3, 8, 10],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [1, 2, 3,..."
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Parsing hard classification response
{
  "response": "```json\n{\n    \"home_page\": [5],\n    \"about_us\": [12],\n    \"terms_and_condition\": [],\n    \"returns_cancellation_exchange\": [1, 2, 3, 8, 10],\n    \"privacy_policy\": [],\n    \"shipping_delivery\": [1, 2, 3, 4, 8, 10],\n    \"contact_us\": [0],\n    \"catalogue\": [1, 2, 3, 8, 10, 11],\n    \"urls_not_reachable\": [],\n    \"Unreachable_via_tool\": [6, 7, 9]\n}\n```"
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 23
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Hard classification reachability results
{
  "unreachable_urls_count": 3,
  "reachable_urls_count": 20,
  "unreachable_urls": [
    6,
    7,
    9
  ],
  "website": "https://drynotch.com",
  "backup_flow_trigger": true
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Verified indices dictionary created
{
  "indices_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_indices": 23
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Verified URLs dictionary created with social media merged
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_verified_urls": 23,
  "social_media_merged": true
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Completed hard classification verification with social media merge
{
  "verified_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "total_verified_urls": 23,
  "policy_hard_classified": true,
  "social_media_soft_classified": true
}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Saving hard classification results to database
2025-07-29 20:58:19,878 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 20:58:19,878 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 20:58:19,878 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:58:19,878 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 20:58:19,878 INFO sqlalchemy.engine.Engine [cached since 28.33s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 20:58:19,878 - sqlalchemy.engine.Engine - INFO - [cached since 28.33s ago] ('208378df-sdfgcfeddcdsdc',)
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Retrieved 48 URL records for hard classification update
2025-07-29 20:58:19,881 INFO sqlalchemy.engine.Engine UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 20:58:19,881 - sqlalchemy.engine.Engine - INFO - UPDATE website_urls_gemini SET hard_class=? WHERE website_urls_gemini.id = ?
2025-07-29 20:58:19,881 INFO sqlalchemy.engine.Engine [generated in 0.00020s] [('["home_page"]', 2546), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2548), ('["about_us"]', 2549), ('["Unreachable_via_tool"]', 2553), ('["catalogue"]', 2554), ('["contact_us"]', 2556), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2557), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2558)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2591), ('["shipping_delivery"]', 2593)]
2025-07-29 20:58:19,881 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] [('["home_page"]', 2546), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2548), ('["about_us"]', 2549), ('["Unreachable_via_tool"]', 2553), ('["catalogue"]', 2554), ('["contact_us"]', 2556), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2557), ('["returns_cancellation_exchange", "shipping_delivery", "catalogue"]', 2558)  ... displaying 10 of 13 total bound parameter sets ...  ('["Unreachable_via_tool"]', 2591), ('["shipping_delivery"]', 2593)]
2025-07-29 20:58:19,882 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 20:58:19,882 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Updated 23 database records with hard classification
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Hard classification summary: 23 total URLs across 10 categories
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Hard classification category breakdown: {'home_page': 1, 'about_us': 1, 'terms_and_condition': 0, 'returns_cancellation_exchange': 5, 'privacy_policy': 0, 'shipping_delivery': 6, 'contact_us': 1, 'catalogue': 6, 'urls_not_reachable': 0, 'Unreachable_via_tool': 3}
[2025-07-29 20:58:19][url_classification_208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully saved hard classification results to database
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Hard classification completed
{
  "categories_found": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ]
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Hard classification results - detailed breakdown
{
  "category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "total_reachable_urls": 23,
  "priority_reachable_urls": 8,
  "total_unreachable_urls": 0,
  "unreachable_percentage": "0.0%"
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Some URLs are reachable - continuing with normal flow
{
  "total_reachable_urls": 23,
  "priority_urls_found": 8,
  "decision": "NORMAL_FLOW_CONTINUES",
  "reachable_categories": [
    "home_page",
    "about_us",
    "returns_cancellation_exchange",
    "shipping_delivery",
    "contact_us",
    "catalogue"
  ]
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting priority URL filtering
{
  "input_categories": [
    "home_page",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "catalogue",
    "urls_not_reachable",
    "Unreachable_via_tool"
  ],
  "priority_categories_defined": [
    "products",
    "about_us",
    "catalogue",
    "home_page"
  ]
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Filtering priority URLs for MCC analysis - input analysis
{
  "total_categories": 10,
  "input_category_counts": {
    "home_page": 1,
    "about_us": 1,
    "terms_and_condition": 0,
    "returns_cancellation_exchange": 5,
    "privacy_policy": 0,
    "shipping_delivery": 6,
    "contact_us": 1,
    "catalogue": 6,
    "urls_not_reachable": 0,
    "Unreachable_via_tool": 3
  },
  "priority_categories": [
    "products",
    "about_us",
    "catalogue",
    "home_page"
  ],
  "max_urls_for_mcc": 18
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: URLs per category calculated: 18
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'home_page': 1 URLs
{
  "category": "home_page",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Added 1 URLs for priority category 'home_page'
{
  "category": "home_page",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'about_us': 1 URLs
{
  "category": "about_us",
  "url_count": 1,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Added 1 URLs for priority category 'about_us'
{
  "category": "about_us",
  "added_urls": 1,
  "total_available": 1,
  "urls_per_category_limit": 18
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'terms_and_condition': 0 URLs
{
  "category": "terms_and_condition",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'returns_cancellation_exchange': 5 URLs
{
  "category": "returns_cancellation_exchange",
  "url_count": 5,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'privacy_policy': 0 URLs
{
  "category": "privacy_policy",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'shipping_delivery': 6 URLs
{
  "category": "shipping_delivery",
  "url_count": 6,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'contact_us': 1 URLs
{
  "category": "contact_us",
  "url_count": 1,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'catalogue': 6 URLs
{
  "category": "catalogue",
  "url_count": 6,
  "is_priority": true,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Added 6 URLs for priority category 'catalogue'
{
  "category": "catalogue",
  "added_urls": 6,
  "total_available": 6,
  "urls_per_category_limit": 18
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'urls_not_reachable': 0 URLs
{
  "category": "urls_not_reachable",
  "url_count": 0,
  "is_priority": false,
  "has_urls": false
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] DEBUG: Processing category 'Unreachable_via_tool': 3 URLs
{
  "category": "Unreachable_via_tool",
  "url_count": 3,
  "is_priority": false,
  "has_urls": true
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Priority URLs filtered
{
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8,
  "fallback_applied": false
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Priority URL filtering completed
{
  "priority_url_counts": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "total_priority_urls": 8
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Data preparation completed
{
  "total_classified_urls": 23,
  "priority_urls_count": 8
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully unpacked data preparation result
{
  "classified_urls_type": "<class 'dict'>",
  "priority_urls_type": "<class 'dict'>",
  "classified_urls_is_none": false,
  "priority_urls_is_none": false
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 2 completed in 26.73 seconds
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 3: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_url_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_priority_urls": 8
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Starting MCC classification
{
  "website": "https://drynotch.com",
  "priority_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ]
}
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 🔍 Extracting text content from priority URLs for analysis
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📄 Extracting text from home_page: https://drynotch.com
[2025-07-29 20:58:19][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com
[2025-07-29 20:58:19][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com",
  "timeout": 45
}
[2025-07-29 20:58:30][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com"
}
[2025-07-29 20:58:30][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com",
  "retry": 0
}
[2025-07-29 20:58:30][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 20:58:39][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com"
}2025-07-29 21:00:40,650 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 21:00:47,068 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 21:00:47,070 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 20:58:39][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com",
  "retry": 1
}
[2025-07-29 20:58:39][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 20:58:39][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 20:59:04][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://drynotch.com
[2025-07-29 20:59:04][208378df-sdfgcfeddcdsdc][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for home_page, trying requests method
[2025-07-29 20:59:05][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Text extracted from home_page: 5000 characters
[2025-07-29 20:59:05][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📄 Extracting text from about_us: https://drynotch.com/pages/about-us
[2025-07-29 20:59:05][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com/pages/about-us
[2025-07-29 20:59:05][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com/pages/about-us",
  "timeout": 45
}
[2025-07-29 20:59:20][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/pages/about-us"
}
[2025-07-29 20:59:20][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/pages/about-us",
  "retry": 0
}
[2025-07-29 20:59:20][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 20:59:29][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/pages/about-us"
}
[2025-07-29 20:59:29][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/pages/about-us",
  "retry": 1
}
[2025-07-29 20:59:29][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 20:59:29][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 20:59:50][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://drynotch.com/pages/about-us
[2025-07-29 20:59:50][208378df-sdfgcfeddcdsdc][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for about_us, trying requests method
[2025-07-29 20:59:51][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Text extracted from about_us: 2172 characters
[2025-07-29 20:59:51][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📄 Extracting text from catalogue: https://drynotch.com/products/fiery-trio-co-ord-set
[2025-07-29 20:59:51][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Extracting text using Playwright for: https://drynotch.com/products/fiery-trio-co-ord-set
[2025-07-29 20:59:51][text_extraction][NO_REF] INFO: Starting text extraction
{
  "url": "https://drynotch.com/products/fiery-trio-co-ord-set",
  "timeout": 45
}
[2025-07-29 21:00:10][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/products/fiery-trio-co-ord-set"
}
[2025-07-29 21:00:10][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/products/fiery-trio-co-ord-set",
  "retry": 0
}
[2025-07-29 21:00:10][text_extraction][NO_REF] INFO: Retrying direct connection in 1.50 seconds
[2025-07-29 21:00:21][text_extraction][NO_REF] WARNING: JavaScript security challenge detected
{
  "url": "https://drynotch.com/products/fiery-trio-co-ord-set"
}
[2025-07-29 21:00:21][text_extraction][NO_REF] WARNING: Page appears to be blocked with direct connection
{
  "url": "https://drynotch.com/products/fiery-trio-co-ord-set",
  "retry": 1
}
[2025-07-29 21:00:21][text_extraction][NO_REF] INFO: Attempting proxy connection for text extraction as fallback
[2025-07-29 21:00:21][text_extraction][NO_REF] INFO: Using webshare proxy with valid credentials
[2025-07-29 21:00:36][text_extraction][NO_REF] ERROR: Text extraction timed out after 45 seconds for URL: https://drynotch.com/products/fiery-trio-co-ord-set
[2025-07-29 21:00:36][208378df-sdfgcfeddcdsdc][NO_REF] WARNING: ⚠️ Playwright extraction insufficient for catalogue, trying requests method
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Text extracted from catalogue: 5000 characters
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📊 Text extraction completed: 3 categories with content
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Website information attempt 1
{
  "website": "https://drynotch.com"
}
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📊 URLs being passed to Gemini for website summary
{
  "total_urls_count": 8,
  "urls_breakdown_by_category": {
    "home_page": 1,
    "about_us": 1,
    "catalogue": 6
  },
  "priority_urls_structure": {
    "home_page": [
      "https://drynotch.com"
    ],
    "about_us": [
      "https://drynotch.com/pages/about-us"
    ],
    "catalogue": [
      "https://drynotch.com/products/fiery-trio-co-ord-set",
      "https://drynotch.com/products/work-it-out-straight-fit-pants"
    ]
  },
  "max_urls_for_mcc_limit": 18,
  "extracted_content_categories": [
    "home_page",
    "about_us",
    "catalogue"
  ],
  "total_extracted_text_length": 12172
}
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Using extracted text content for website analysis
[2025-07-29 21:00:37][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Calling Gemini API for website information...
[2025-07-29 21:00:40][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 60,
  "max_retries": 3,
  "prompt_length": 9766,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-29 21:00:40][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 21:00:47][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=306 candidates_tokens_details=None prompt_token_count=2459 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=2459
)] thoughts_token_count=532 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3297 traffic_type=None
[2025-07-29 21:00:47][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 1313,
  "finish_reason": "STOP"
}
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Website info response length: 1313
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Received Gemini API response for website information
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "is_valid_website",
    "website_redirection",
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📋 WEBSITE SUMMARY OUTPUT FROM GEMINI
{
  "website": "https://drynotch.com",
  "total_urls_analyzed": 8,
  "summary_content": {
    "product_services": "Drynotch offers a range of activewear products including Co-ord sets, Tops, Sports Bras, Crop tops, and Bottoms. Specific co-ord sets mentioned are Fiery Trio Co-ord Set, Citrus Blaze Co-ord Set, Flex-on Quarter Zip Co-ord Set, Neon Attack Co-ord Set, Sleek Monochrome Set, and Blue Crush Co-ord Set. The Fiery Trio Co-ord Set is described as a long-sleeve crop top with a round neckline, bold chest cut-out, and thumbhole cuffs, made from breathable, four-way stretch fabric.",
    "line_of_business": "Activewear/Apparel Retail",
    "customers": "The target customers are individuals (B2C) who are looking for high-quality, stylish, comfortable, and durable activewear for workouts, running, or everyday activities. The brand aims to empower customers to feel confident and inspired during their workouts and beyond.",
    "website_description": "Drynotch is an online activewear retailer that provides meticulously crafted, high-quality, and stylish activewear designed to offer unmatched comfort, superior fit, and durability. The brand focuses on blending fashion and function, aiming to empower customers during their workouts and daily activities by offering premium activewear at affordable prices."
  },
  "summary_lengths": {
    "product_services_chars": 476,
    "line_of_business_chars": 25,
    "customers_chars": 269,
    "website_description_chars": 357
  }
}2025-07-29 21:00:54,124 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-07-29 21:01:03,568 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-07-29 21:01:03,578 - google_genai.models - INFO - AFC remote call 1 is done.

[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Website information extracted successfully
{
  "info_keys": [
    "product_services",
    "line_of_business",
    "customers",
    "website_description"
  ]
}
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC classification attempt 1
{
  "website": "https://drynotch.com"
}
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Loading CSV data files...
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Loaded special_mccs.csv with 57 rows
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Loaded combined_mcc.csv with 303 rows
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: CSV data processing completed
[2025-07-29 21:00:52][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Calling Gemini API for MCC classification...
[2025-07-29 21:00:54][mcc_analysis_unknown][NO_REF] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 60,
  "max_retries": 3,
  "prompt_length": 223226,
  "context": {
    "task_type": "mcc_analysis"
  }
}
[2025-07-29 21:00:54][mcc_analysis_unknown][NO_REF] INFO: Gemini API attempt 1/3
[2025-07-29 21:01:03][mcc_analysis_unknown][NO_REF] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=225 candidates_tokens_details=None prompt_token_count=46683 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=46683
)] thoughts_token_count=1090 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=47998 traffic_type=None
[2025-07-29 21:01:03][mcc_analysis_unknown][NO_REF] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 874,
  "finish_reason": "STOP"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Received Gemini API response for MCC classification
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Successfully parsed JSON response
{
  "keys": [
    "mcc",
    "business_desc",
    "business_category",
    "reason",
    "website"
  ]
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC classification retrieved successfully
{
  "mcc": "5655",
  "business_desc": "An online activewear retailer offering high-quality, stylish, and comfortable sports apparel, including co-ord sets, tops, sports bras, crop tops, and bottoms, for individuals engaged in workouts, running, or daily activities.",
  "business_category": "Sports Apparel Retailer",
  "reason": "The business, Drynotch, is explicitly described as an 'online activewear retailer' with its 'line_of_business' stated as 'Activewear/Apparel Retail'. The 'product_services' include 'activewear products including Co-ord sets, Tops, Sports Bras, Crop tops, and Bottoms' for 'workouts, running, or everyday activities'. This perfectly aligns with the 'special_mcc_data' entry for MCC 5655, 'Sports apparels', which is defined as 'Business selling sports apparel'. Therefore, 5655 is the most specific and appropriate MCC."
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 3: MCC classification completed in 168.69 seconds
{
  "website_info_available": true,
  "mcc_info_available": true,
  "processing_time": "168.69s"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ✅ Website content appears valid - continuing with normal flow
{
  "inactive_fields": 0,
  "valid_fields": 4,
  "threshold_check": "passed"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 4: Saving results to database
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Saving MCC analysis results to database
{
  "website": "https://drynotch.com"
}
2025-07-29 21:01:08,581 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 21:01:08,581 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id, mcc_analysis_gemini.website, mcc_analysis_gemini.scrape_request_ref_id, mcc_analysis_gemini.result_status, mcc_analysis_gemini.mcc_code, mcc_analysis_gemini.business_category, mcc_analysis_gemini.business_description, mcc_analysis_gemini.reasoning, mcc_analysis_gemini.created_at, mcc_analysis_gemini.started_at, mcc_analysis_gemini.completed_at, mcc_analysis_gemini.failed_at, mcc_analysis_gemini.last_updated, mcc_analysis_gemini.error_message, mcc_analysis_gemini.details, mcc_analysis_gemini.org_id, mcc_analysis_gemini.processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.scrape_request_ref_id = ?
2025-07-29 21:01:08,581 INFO sqlalchemy.engine.Engine [cached since 197s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 21:01:08,581 - sqlalchemy.engine.Engine - INFO - [cached since 197s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 21:01:08,583 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,583 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET mcc_code=?, business_category=?, business_description=?, reasoning=?, completed_at=?, details=?, processing_status=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,583 INFO sqlalchemy.engine.Engine [generated in 0.00019s] ('5655', 'Sports Apparel Retailer', '', "The business, Drynotch, is explicitly described as an 'online activewear retailer' with its 'line_of_business' stated as 'Activewear/Apparel Retail'. ... (220 characters truncated) ... try for MCC 5655, 'Sports apparels', which is defined as 'Business selling sports apparel'. Therefore, 5655 is the most specific and appropriate MCC.", '2025-07-29T21:01:08.582295Z', '{"website": "https://drynotch.com", "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc", "website_info": {"product_services": "Drynotch offers a range ... (3697 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 34)
2025-07-29 21:01:08,583 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] ('5655', 'Sports Apparel Retailer', '', "The business, Drynotch, is explicitly described as an 'online activewear retailer' with its 'line_of_business' stated as 'Activewear/Apparel Retail'. ... (220 characters truncated) ... try for MCC 5655, 'Sports apparels', which is defined as 'Business selling sports apparel'. Therefore, 5655 is the most specific and appropriate MCC.", '2025-07-29T21:01:08.582295Z', '{"website": "https://drynotch.com", "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc", "website_info": {"product_services": "Drynotch offers a range ... (3697 characters truncated) ... lysis", "content_availability_status": "content_available", "fallback_method_used": false, "text_extraction_used": false, "insufficient_data": false}', 'COMPLETED', 34)
2025-07-29 21:01:08,583 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 21:01:08,583 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 21:01:08,863 - httpx - INFO - HTTP Request: PATCH https://bffapi.biztel.ai/api/mcc/results "HTTP/1.1 401 "
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC analysis results saved successfully
{
  "analysis_id": 34
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 🚀 Preparing MCC results webhook with correct format
{
  "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc",
  "website": "https://drynotch.com",
  "analysis_id": 34
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📦 MCC WEBHOOK PAYLOAD CONSTRUCTION - STEP BY STEP
{
  "construction_step": "1_mcc_payload_created",
  "timestamp": "2025-07-29T21:01:08.586250",
  "website": "https://drynotch.com",
  "scrape_request_ref_id": "208378df-sdfgcfeddcdsdc",
  "mcc_code": "5655",
  "business_category": "Sports Apparel Retailer",
  "business_description_length": 0,
  "payload_keys": [
    "website",
    "createdDate",
    "status",
    "scrapeRequestUuid",
    "mcc",
    "manualMcc",
    "businessCategory",
    "businessDescription"
  ],
  "payload_structure": {
    "website": "https://drynotch.com",
    "scrapeRequestUuid": "208378df-sdfgcfeddcdsdc",
    "status": "COMPLETED",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports Apparel Retailer",
    "businessDescription_length": 0,
    "createdDate": "2025-07-29T21:01:08.586238Z"
  },
  "complete_payload_json": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T21:01:08.586238Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-sdfgcfeddcdsdc",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports Apparel Retailer",
    "businessDescription": ""
  }
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📤 Sending MCC PATCH request with clean headers - DETAILED DEBUG
{
  "url": "https://bffapi.biztel.ai/api/mcc/results",
  "method": "PATCH",
  "payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T21:01:08.586238Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-sdfgcfeddcdsdc",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports Apparel Retailer",
    "businessDescription": ""
  },
  "headers_being_sent": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "api_key_in_headers": "12345678",
  "content_type_in_headers": "application/json",
  "api_key_length": 8
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 🔥 MCC PATCH REQUEST - FINAL TRANSMISSION
{
  "construction_step": "2_sending_mcc_request",
  "timestamp": "2025-07-29T21:01:08.592185",
  "about_to_send": true,
  "final_url": "https://bffapi.biztel.ai/api/mcc/results",
  "final_method": "PATCH",
  "final_headers": {
    "X-API-KEY": "12345678",
    "Content-Type": "application/json"
  },
  "final_payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T21:01:08.586238Z",
    "status": "COMPLETED",
    "scrapeRequestUuid": "208378df-sdfgcfeddcdsdc",
    "mcc": 5655,
    "manualMcc": -1,
    "businessCategory": "Sports Apparel Retailer",
    "businessDescription": ""
  },
  "final_timeout": 30.0,
  "curl_equivalent": "curl -X PATCH 'https://bffapi.biztel.ai/api/mcc/results' -H 'Content-Type: application/json' -H 'X-API-KEY: 12345678' -d '{\"website\": \"https://drynotch.com\", \"createdDate\": \"2025-07-29T21:01:08.586238Z\", \"status\": \"COMPLETED\", \"scrapeRequestUuid\": \"208378df-sdfgcfeddcdsdc\", \"mcc\": 5655, \"manualMcc\": -1, \"businessCategory\": \"Sports Apparel Retailer\", \"businessDescription\": \"\"}'"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: ⚡ MCC PATCH RESPONSE - IMMEDIATE RESULT
{
  "construction_step": "3_mcc_response_received",
  "timestamp": "2025-07-29T21:01:08.863905",
  "response_status_code": 401,
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Tue, 29 Jul 2025 15:31:08 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "response_text": "",
  "response_success": false,
  "response_size": 0,
  "request_url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📥 MCC PATCH response received
{
  "status_code": 401,
  "response_text": "",
  "response_headers": {
    "server": "nginx/1.22.1",
    "date": "Tue, 29 Jul 2025 15:31:08 GMT",
    "content-length": "0",
    "connection": "keep-alive",
    "vary": "Origin, Access-Control-Request-Method, Access-Control-Request-Headers",
    "x-content-type-options": "nosniff",
    "x-xss-protection": "0",
    "cache-control": "no-cache, no-store, max-age=0, must-revalidate",
    "pragma": "no-cache",
    "expires": "0",
    "x-frame-options": "DENY",
    "www-authenticate": "Basic realm=\"Realm\""
  },
  "url": "https://bffapi.biztel.ai/api/mcc/results"
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] ERROR: ❌ MCC results webhook failed - Authentication error
{
  "error": "{'status_code': 401, 'response': '', 'url': 'https://bffapi.biztel.ai/api/mcc/results', 'error': 'Invalid API key - check BIZTEL_API_KEY in .env file', 'api_key_length': 8}",
  "error_type": "dict"
}
Traceback for analysis 208378df-sdfgcfeddcdsdc:
Error printing traceback: 'dict' object has no attribute '__traceback__'
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 🔄 Attempting to send default values despite authentication error
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: 📋 Created default payload for authentication error
{
  "payload": {
    "website": "https://drynotch.com",
    "createdDate": "2025-07-29T21:01:08.864152Z",
    "status": "FAILED",
    "scrapeRequestUuid": "208378df-sdfgcfeddcdsdc",
    "mcc": -1,
    "manualMcc": -1,
    "businessCategory": "authentication_error",
    "businessDescription": "webhook authentication failed"
  }
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] WARNING: ⚠️ MCC results webhook failed
{
  "analysis_id": 34
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Saving detailed MCC URL classification results
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Saving MCC URL classification results
{
  "analysis_id": 34,
  "soft_categories": 16,
  "hard_categories": 10
}
2025-07-29 21:01:08,865 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 21:01:08,865 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 21:01:08,866 INFO sqlalchemy.engine.Engine SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 21:01:08,866 - sqlalchemy.engine.Engine - INFO - SELECT website_urls_gemini.id, website_urls_gemini.scrape_request_ref_id, website_urls_gemini.website, website_urls_gemini.url, website_urls_gemini.depth, website_urls_gemini.soft_class, website_urls_gemini.hard_class, website_urls_gemini.priority_url, website_urls_gemini.extracted_text, website_urls_gemini.img_url, website_urls_gemini.policy, website_urls_gemini.registered_name, website_urls_gemini.org_id 
FROM website_urls_gemini 
WHERE website_urls_gemini.scrape_request_ref_id = ?
2025-07-29 21:01:08,866 INFO sqlalchemy.engine.Engine [cached since 195.7s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 21:01:08,866 - sqlalchemy.engine.Engine - INFO - [cached since 195.7s ago] ('208378df-sdfgcfeddcdsdc',)
2025-07-29 21:01:08,871 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,871 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,871 INFO sqlalchemy.engine.Engine [generated in 0.00028s (insertmanyvalues) 1/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-29T21:01:08.867675Z', 'default')
2025-07-29 21:01:08,871 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s (insertmanyvalues) 1/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/contact', 1, 'contact_us', 'contact_us', 'contact_us', 0, 0, '2025-07-29T21:01:08.867675Z', 'default')
2025-07-29 21:01:08,882 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,882 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine [insertmanyvalues 2/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.867899Z', 'default')
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 2/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/fiery-trio-co-ord-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.867899Z', 'default')
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine [insertmanyvalues 3/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868011Z', 'default')
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 3/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/work-it-out-straight-fit-pants', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868011Z', 'default')
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine [insertmanyvalues 4/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/sleek-monochrome-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868114Z', 'default')
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 4/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/sleek-monochrome-set', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868114Z', 'default')
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine [insertmanyvalues 5/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/shipping', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-29T21:01:08.868209Z', 'default')
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 5/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/shipping', 1, 'shipping_delivery', 'shipping_delivery', 'shipping_delivery', 0, 0, '2025-07-29T21:01:08.868209Z', 'default')
2025-07-29 21:01:08,883 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,883 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,884 INFO sqlalchemy.engine.Engine [insertmanyvalues 6/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-29T21:01:08.868302Z', 'default')
2025-07-29 21:01:08,884 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 6/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com', 1, 'home_page', 'home_page', 'home_page', 1, 1, '2025-07-29T21:01:08.868302Z', 'default')
2025-07-29 21:01:08,884 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,884 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,884 INFO sqlalchemy.engine.Engine [insertmanyvalues 7/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868399Z', 'default')
2025-07-29 21:01:08,884 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 7/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/refund-policy', 1, 'returns_cancellation_exchange', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868399Z', 'default')
2025-07-29 21:01:08,884 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,884 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,884 INFO sqlalchemy.engine.Engine [insertmanyvalues 8/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868500Z', 'default')
2025-07-29 21:01:08,884 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 8/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/terms-of-service', 1, 'terms_and_condition', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868500Z', 'default')
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine [insertmanyvalues 9/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868593Z', 'default')
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 9/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/blue-crush-co-ord-set#judgeme_product_reviews', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868593Z', 'default')
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine [insertmanyvalues 10/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/privacy-policy', 1, 'privacy_policy', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868691Z', 'default')
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 10/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/policies/privacy-policy', 1, 'privacy_policy', 'Unreachable_via_tool', 'Unreachable_via_tool', 0, 0, '2025-07-29T21:01:08.868691Z', 'default')
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine [insertmanyvalues 11/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868786Z', 'default')
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 11/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/products/notchflex-high-waist-leggings-blue-crush', 1, 'products', 'returns_cancellation_exchange,shipping_delivery,catalogue', 'returns_cancellation_exchange,shipping_delivery,catalogue', 1, 0, '2025-07-29T21:01:08.868786Z', 'default')
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,885 INFO sqlalchemy.engine.Engine [insertmanyvalues 12/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/collections', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-29T21:01:08.868882Z', 'default')
2025-07-29 21:01:08,885 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 12/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/collections', 1, 'catalogue', 'catalogue', 'catalogue', 1, 0, '2025-07-29T21:01:08.868882Z', 'default')
2025-07-29 21:01:08,886 INFO sqlalchemy.engine.Engine INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,886 - sqlalchemy.engine.Engine - INFO - INSERT INTO mcc_url_classification_gemini (mcc_analysis_id, scrape_request_ref_id, url, url_depth, soft_classification, hard_classification, final_classification, is_priority_url, is_social_media, created_at, org_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id
2025-07-29 21:01:08,886 INFO sqlalchemy.engine.Engine [insertmanyvalues 13/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-29T21:01:08.868989Z', 'default')
2025-07-29 21:01:08,886 - sqlalchemy.engine.Engine - INFO - [insertmanyvalues 13/13 (ordered; batch not supported)] (34, '208378df-sdfgcfeddcdsdc', 'https://drynotch.com/pages/about-us', 1, 'about_us', 'about_us', 'about_us', 1, 0, '2025-07-29T21:01:08.868989Z', 'default')
2025-07-29 21:01:08,890 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 21:01:08,890 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 21:01:08,893 - app.routers.mcc_analysis - INFO - MCC analysis task 34 completed with status: COMPLETED
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC URL classification results saved successfully
{
  "analysis_id": 34,
  "urls_saved": 13
}
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC URL classification results saved successfully
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: Step 4 completed in 0.31 seconds
[2025-07-29 21:01:08][208378df-sdfgcfeddcdsdc][NO_REF] INFO: MCC analysis completed successfully
{
  "analysis_id": 34,
  "processing_time": "195.74s",
  "mcc": [
    "5655"
  ]
}
2025-07-29 21:01:08,895 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-29 21:01:08,895 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-29 21:01:08,895 INFO sqlalchemy.engine.Engine SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,895 - sqlalchemy.engine.Engine - INFO - SELECT mcc_analysis_gemini.id AS mcc_analysis_gemini_id, mcc_analysis_gemini.website AS mcc_analysis_gemini_website, mcc_analysis_gemini.scrape_request_ref_id AS mcc_analysis_gemini_scrape_request_ref_id, mcc_analysis_gemini.result_status AS mcc_analysis_gemini_result_status, mcc_analysis_gemini.mcc_code AS mcc_analysis_gemini_mcc_code, mcc_analysis_gemini.business_category AS mcc_analysis_gemini_business_category, mcc_analysis_gemini.business_description AS mcc_analysis_gemini_business_description, mcc_analysis_gemini.reasoning AS mcc_analysis_gemini_reasoning, mcc_analysis_gemini.created_at AS mcc_analysis_gemini_created_at, mcc_analysis_gemini.started_at AS mcc_analysis_gemini_started_at, mcc_analysis_gemini.completed_at AS mcc_analysis_gemini_completed_at, mcc_analysis_gemini.failed_at AS mcc_analysis_gemini_failed_at, mcc_analysis_gemini.last_updated AS mcc_analysis_gemini_last_updated, mcc_analysis_gemini.error_message AS mcc_analysis_gemini_error_message, mcc_analysis_gemini.details AS mcc_analysis_gemini_details, mcc_analysis_gemini.org_id AS mcc_analysis_gemini_org_id, mcc_analysis_gemini.processing_status AS mcc_analysis_gemini_processing_status 
FROM mcc_analysis_gemini 
WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,895 INFO sqlalchemy.engine.Engine [cached since 195.8s ago] (34,)
2025-07-29 21:01:08,895 - sqlalchemy.engine.Engine - INFO - [cached since 195.8s ago] (34,)
2025-07-29 21:01:08,897 INFO sqlalchemy.engine.Engine UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,897 - sqlalchemy.engine.Engine - INFO - UPDATE mcc_analysis_gemini SET completed_at=? WHERE mcc_analysis_gemini.id = ?
2025-07-29 21:01:08,897 INFO sqlalchemy.engine.Engine [generated in 0.00016s] ('2025-07-29T21:01:08.896593Z', 34)
2025-07-29 21:01:08,897 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] ('2025-07-29T21:01:08.896593Z', 34)
2025-07-29 21:01:08,897 INFO sqlalchemy.engine.Engine COMMIT
2025-07-29 21:01:08,897 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-29 21:01:08,905 - app.routers.mcc_analysis - INFO - Updated MccAnalysis 34 final status to: COMPLETED
2025-07-29 21:01:08,905 - app.routers.mcc_analysis - INFO - === BACKGROUND TASK COMPLETED SUCCESSFULLY for analysis ID: 34 ===
